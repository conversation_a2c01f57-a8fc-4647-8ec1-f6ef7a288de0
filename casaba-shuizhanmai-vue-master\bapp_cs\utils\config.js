import variable from "./variable.js";
var host = variable.host;
var host3 = variable.host3

var config = {
	// 校验短信剩余量
	storesmsinfocheck: `${host}/szmb/storesmsinfo/check`,

	// 订单列表待派单 新
	// 月结客户汇总
	selectuseryfcollect: `${host}/szmb/newuseryfcontroller/selectUserMonthly`,
	// 月结客户列表
	selectuseryflist: `${host}/szmb/newuseryfcontroller/selectuseryflist`,
	// 今日订单 已送达 押桶申请中
	newGetOrder: `${host}/szmb/szmborder/todayorderapp`,
	//银行转账权限开关查询
	checkBankPower: `${host}/szmb/szmbserviceset/perfectbank`,
	//银行转账金额查询
	banktransferStoreApp: `${host}/szmb/user/banktransferStoreApp`,
	//银行转账客户列表
	banktransferStoreUserListApp: `${host}/szmb/user/banktransferStoreUserListApp`,
	//银行转账客户详情
	banktransferListApp: `${host}/szmb/user/banktransferListApp`,
	//银行转账客户详情
	banktransferApp: `${host}/szmb/user/banktransferApp`,
	//银行转账确认收款
	banktransferConfirmCollection: `${host}/szmb/user/banktransferConfirmCollection`,
	//月结客户列表
	monthlyOrderListApp: `${host}/szmb/newuseryfcontroller/monthlyOrderListApp`,
	//月结客户
	monthlyApp: `${host}/szmb/newuseryfcontroller/monthlyApp`,
	//总营业额
	findTurnoverDetail: `${host}/szmb/newordercontroller/findTurnoverDetail`,
	//总营业额线上订单详情
	selectorderonline: `${host}/szmb/newordercontroller/selectorderonline2`,
	//总营业额水站代客下单详情
	selectorderusersend2: `${host}/szmb/newordercontroller/selectorderusersend2`,
	//总营业额派单员代客下单详情
	selectorderdelivery2: `${host}/szmb/newordercontroller/selectorderdelivery2`,
	//总营业额水票收入详情
	slectorderwater2: `${host}/szmb/newordercontroller/slectorderwater2`,
	//总营业服务收入详情
	selectorderservice2: `${host}/szmb/newordercontroller/selectorderservice2`,
	//总营业线上押桶收入详情
	selectorderbuck2: `${host}/szmb/newordercontroller/selectorderbuck2`,
	//总营业回桶补差价收入详情
	selectrepaymoney2: `${host}/szmb/newordercontroller/selectrepaymoney2`,
	//添加历史资产维护
	addMaintain: `${host}/szmb/historyMaintain/addMaintain`,
	//根据水站id和用户id获取历史资产维护
	getMaintain: `${host}/szmb/historyMaintain/getMaintain`,
	//修改历史资产维护
	editMaintain: `${host}/szmb/historyMaintain/editMaintain`,
	//获取水站桶品牌信息
	getBrandInfo: `${host}/szmb/historyMaintain/getBrandInfo`,
	//获取水站商品分类列表
	getAllClassifys: `${host}/szmcproductclassifycontroller/getAllClassifys`,
	//添加水站还款信息
	insertfundlist: `${host}/szmb/newuseryfcontroller/insertfundlist`,
	newuseryfcontrollerinsertprice: `${host}/szmb/newuseryfcontroller/insertprice`,
	//查询水站还款信息
	findMonthPayList: `${host}/szmb/newuseryfcontroller/findMonthPayList`,

	// 检查是否为桶装水
	selectclasssname: `${host}/szmb/newinsertproductcontroller/selectclasssname`,
	// 查询商品标签接口
	selectstorelable: `${host}/szmb/newinsertproductcontroller/selectstorelable`,
	// 获取水站信息
	selectstoredeatil: `${host}/szmb/szmbhomepagecontroller/selectstoredeatil`,
	// 桶数据
	selectstorebuck: `${host}/szmb/szmbhomepagecontroller/selectstorebuck`,
	// 库存数据
	selectstoreinventory: `${host}/szmb/szmbhomepagecontroller/selectstoreinventory`,
	// 订单数据
	selectstoreorder: `${host}/szmb/szmbhomepagecontroller/selectstoreorder`,
	// 营业数据
	selectstorebusiness: `${host}/szmb/szmbhomepagecontroller/selectstorebusiness`,

	// 查询自己的快捷方式
	selectstoretotal: `${host}/szmb/szmbhomepagecontroller/selectstoretotal`,
	// 查询所有快捷方式
	selecttotal: `${host}/szmb/szmbhomepagecontroller/selecttotal`,
	// 提交快捷方式 
	insertstoreshorot: `${host}/szmb/szmbhomepagecontroller/insertstoreshorot`,
	// 新接口 王肖飞



	/*
	 ** 派单员接口 Start
	 */
	// 2019-05-15

	// 检查版本更新状态
	updateversion: `${host}/dpt/appversioncontroller/updateversion`,
	// 检查版本更新状态
	selectversion: `${host}/dpt/appversioncontroller/selectversion`,
	// 撤销指派新接口
	// cancelDriveryOrder: `${host}/szmb/szmborder/revocation`,
	// 撤销指派新接口
	cancelDriveryOrder: `${host}/szmb/szmsendmembercontroller/back`,
	// 导出Excel
	exportExcel: `${host}/api/excal/addexcal`,

	// 更新已读状态
	updateReadList: `${host}/szmb/szmbstorecontroller/readpendingitem`,
	// 未读消息接口
	getUnreadList: `${host}/szmb/szmbstorecontroller/pendingitem`,
	getUnreadListcount: `${host}/szmb/szmbstorecontroller/pendingitemcount`,


	// 查询某个帮助类别下的帮助中心
	getHelpAll: `${host}/szmb/helpcontroller/selectall`,
	// 删除帮助POST
	delHelpItem: `${host}/szmb/helpcontroller/delitem`,
	// 新增自定义帮助POST
	addStoreHelp: `${host}/szmb/helpcontroller/addnewitem`,
	// 模板新增到水站模板下POST
	addHelpToStore: `${host}/szmb/helpcontroller/additem`,
	// 修改帮助POST
	editHelpItem: `${host}/szmb/helpcontroller/updateitem`,


   // updatedeliveryinfo: `${host}/szmb/newdebtbuckcontroller/updatedeliveryinfo`,
	selfList: `${host}/szmb/self/list`,

	// 订单管理 查询全部订单
	allOrderList: `${host}/szmb/szmborder/allorder`,
	allOrderListPc: `${host}/szmb/szmborder/orderlistpc`,
	allOrderListPcanalysis: `${host}/szmb/szmborder/orderlistpcanalysis`,
	  // getAlreadyOrder: `${host}/szmb/szmsendmembercontroller/selectsendmemberbyuserid`,

	//
	deliveryquern: `${host}/szmb/szmstoreapplycontroller/deliveryquern`,
	// 查询自送开关状态
	selectstoreissend: `${host}/szmb/szmbvippricecontroller/selectstoreissend`,
	// 自送开关
	updatestoreisseng: `${host}/szmb/szmbvippricecontroller/updatestoreisseng`,
	// 派单员全部订单
	getDeliveryOrder: `${host}/szmb/szmsendmembercontroller/selectall`,

	// 获取水站信息
	getStoreInfo: `${host}/szmb/szmbstorecontroller/selectstoreid`,
	// 设置水站选择的短信模块 POST
	setStoreSms: `${host}/szmb/szmbsms/setSms`,
	// 获取所有短信模块 POST
	getAllSms: `${host}/szmb/szmbsms/selSms`,

	// 查看派单员回收订单记录 POST /szmb/szmstoreapplycontroller/selectdelivery
	getRecoveryOrder: `${host}/szmb/szmstoreapplycontroller/selectdelivery`,

	// 查看用户的提成模板 POST
	selectdeliveryrule: `${host}/szmb/szmsendmembercontroller/selectdeliveryrule`,

	// 水站审核派单员新增用户 POST
	storeCheckuser: `${host}/szmb/deliveryusercontroller/checkuser`,
	// 水站查看派单员新增用户类别 POST
	selectbyduser: `${host}/szmb/deliveryusercontroller/selectbyduser`,
	//根据用户id查询信息
	selectbyuservo: `${host}/szmcuercontroller/selectbyuservo`,
	// 派单员新增用户 POST
	deliveryAdduser: `${host}/szmb/deliveryusercontroller/adduser`,
	// 派单员查看用户 POST
	// findUserList: `${host}szmb/deliveryusercontroller/selectbyduser`,
	findUserList: `${host}szmb/deliveryusercontroller/selectbydusergai`,
	// 派单员修改用户 POST
	deliveryEditUser: `${host}/szmb/deliveryusercontroller/updateuser`,

	// 商家审核派单员提交的添加用户的申请 POST
	checkDeliveryAdduser: `${host}/szmb/deliveryusercontroller/checkuser`,

	// 年度收入对比
	incomeComparison: `${host}/szmb/totalassets/selectbytwoyeartotal`,
	// 派单员模糊查询 POST
	driverLike: `${host}/szmb/deliveryinfo/selectdelivery`,
	// 修改派单员状态 POST
	eidtDriverState: `${host}/szmb/deliveryinfo/updatdelivery`,

	// 固定派单员接单 POST
	fixedDeliveryOrder: `${host}/szmb/storesendpaycontroller/insertsenddelivery`,
	// 指派派单员 POST
	// selectdelivery: `${host}/szmb/szmsendmembercontroller/insertdeliverydeduct`,
	// 指派派单员 郝震 POST
	selectdelivery: `${host}/szmb/szmborder/selectdeliveryid`,

	// 查看所有店铺的 派单员 POST
	getShopAllDelivery: `${host}/szmb/szmsendmembercontroller/selectdelectstate`,

	// 派单员端-线下订单查看详情接口
	downLineOrderDetail: `${host}/szmb/szmsendmembercontroller/selectlineorderdeatil`,
	//派单员端-线下订单接单
	acceptLineDownOrder: `${host}/szmb/szmsendmembercontroller/selectlinefifm`,

	//派单员-提成-新接口
	getNewCashInfo: `${host}/szmb/szmsendmembercontroller/selectdeliverydeduct`,
	//派单员-已接单-线下-确认送达
	sureLineDownOrder: `${host}/szmb/szmsendmembercontroller/selectlinefifm`,


	// 登录接口 POST
	driverLogin: `${host}/szmb/deliveryusercontroller/login`,
	// 绑定配送员openId POST
	driverBind: `${host}/szmb/deliveryusercontroller/bindphone`,

	// 查看所有可接的单 POST
	getAllOrder: `${host}/szmb/szmsendmembercontroller/selectuseridall`,
	// 查看已经接受全部信息 POST POST
	getAlreadyOrder: `${host}/szmb/szmsendmembercontroller/selectsendmemberbyuserid`,
	// 查看提现信息 POST
	getCashInfo: `${host}/szmb/szmsendmembercontroller/selectfloormoney`,
	//查看订单详情-线下付款-确认收款
	makeSureMoney: `${host}/szmb/szmsendmembercontroller/deliveryaffi`,
	// 缴纳保证金
	payMoney: `${host}/szmb/szmsendmembercontroller/sendPayMoney`,
	/*
	 ** 派单员接口 End
	 */


	/*
	 ** 王肖飞
	 ** Start
	 */
	// 查看店铺的库存和上下架数量 POST POST
	getStoreStock: `${host}/szmb/szmbhomepagecontroller/selectstorestockall`,
	// 首页 年月日 POST
	getYearClassify: `${host}/szmb/szmbhomepagecontroller/selectorderyeat`,
	// 首页 已完成未完成 POST
	
	getYesAndNo: `${host}/szmb/szmbhomepagecontroller/selectsoderdone`,
	// 首页 昨天 今天 POST
	getYTClassify: `${host}/szmb/szmbhomepagecontroller/selectstoretodyanduptody`,

	// 首页 普通和VIP POST
	getVipAndCom: `${host}/szmb/szmbhomepagecontroller/selectstorebyvip`,
	// getVipAndCom: `${host}/szmb/szmbhomepagecontroller/selectuserviplist`,

	// 新增帮助中心问题 POST
	addHelpContent: `${host}/szmb/szmbhelpcontercontroller/inserthelpconter`,
	// 查看帮助中心 POST
	seeHelpContent: `${host}/szmb/szmbhelpcontercontroller/selecthelpconter`,
	// 删除标题和内容 POST
	delHelpContent: `${host}/szmb/szmbhelpcontercontroller/delecthelpconter`,

	// 年度总资产 POST
	yearTotalAssets: `${host}/szmb/totalassets/selectbyoneyear`,
	// 年度总资产对比 POST
	yearTotalAssetsContrast: `${host}/szmb/totalassets/selectbytwoyear`,

	// 派单员 POST
	deliveryBoy: `${host}/szmb/szmbstorefroomandaccontcontroller/selectsendmessage`,
	// 往来账目 POST
	runningAccount: `${host}/szmb/szmbstorefroomandaccontcontroller/selectdebtandbuck`,

	//修改订单状态 POST
	updateOrderState: `${host}/szmb/szmborder/updatestate`,
	// 已完成订单 POST
	getOverOrderList: `${host}/szmb/szmborder/selectfinish`,
	// 订单详情 POST
	getOrderInfo: `${host}/szmb/szmborder/selectorderone`,
	// 更新客户信息 POST
	updateCustomerInfo: `${host}/api/order/updateCustomerInfo`,
	selectZhuanDan: `${host}/szmb/szmborder/selectZhuanDan`,
	// 通过订单号查询操作记录 POST
	selectMsgByOrderNum: `${host}/szmb/msg/selectmsgbyordernum`,

	// 2020-05-26 lcj
	// 待派送订单更改为自送订单 GET
	// merchantDeliverSelf: `${host}/szmb/szmborder/merchantDeliverSelf`,
	
	//水站自送 by johnhe ********
	merchantDeliverSelf: `${host}/szmb/self/add`,
	
	// 自送订单确认送达 GET
	merchantDeliverSelfComplete: `${host}/szmb/szmborder/merchantDeliverSelfComplete`,
	// 自送订单查询补压桶信息 POST
	selectdebtdeatil: `${host}/szmb/newdebtbuckcontroller/selectproductlistall1`,
	// 自送订单补押桶提交  POST
	bucketpay: `${host}/szmb/self/bucketpay`,
      // bucketpay: `${host}/szmc/pledgebuckorderpaycontroller/bucketpay`,
	// 自送订单不回桶 POST
	finishOrder: `${host}/szmb/szmborder/finishOrder`,
	finishOrderBackBucket: `${host}/szmb/szmborder/finishOrderBackBucket`,



	// 根据状态查看订单 POST
	getOrderByState: `${host}/szmb/szmborder/selectstete`,
	// 根据状态查看订单 POST
	getOrderByStateNew: `${host}/szmb/szmborder/selectsteteapp`,
	// 待处理订单 POST
	getOrderingList: `${host}/szmb/szmborder/selectuntreated`,
	
	//  订单确认收款
	updatebanktransfer: `${host}/szmb/user/updatebanktransfer`,


	// 欠款明细 POST
	getFundInfo: `${host}/szmb/fund/selectall`,
	// 欠款总额 POST
	getFund: `${host}/szmb/fund/selectsum`,
	// 获取商家收入 POST
	getShopIncomeInfo: `${host}/szmb/szmstorebillcontroller/selectStorebilldeatil`,
	// 获取商家收入明细 POST
	getShopIncome: `${host}/szmb/newordercontroller/selectordermoney`,
	// 获取库存明细 POST
	getInventoryInfo: `${host}/szmb/inventory/selectall`,
	// 获取库存总数 POST
	getInventory: `${host}/szmb/inventory/selectsum`,
	// 获取钱包信息 POST
	getWalletBalance: `${host}/szmb/szmbstorewallet/selectbyid`,
	// 获取钱包明细 POST
	getWalletInfo: `${host}/szmb/szmbstorewallet/select`,
	// 钱包提现 POST
	walletExtract: `${host}/szmb/szmbstorewallet/insert`,
	// 提现申请记录
	walletExtractRecord: `${host}/szmb/szmbstorewallet/outlist`,

	/*
	 ** 王肖飞
	 ** End
	 */
	// 、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、

	//发送验证码
	sendCode: `${host}/szmcuercontroller/sendcode`,
	//催单发货
	urgeStore: `${host}/szmcuercontroller/urgestore`,
	//商家注册
	shopRegister: `${host}/szmb/szmbstorecontroller/register`,
	//上传图片
	uploadImgs: `${host}/uploadcontroller/upload`,
	// app 绑定openid appBindStoreId
	appBindStoreId: `${host}/szmb/szmbstorecontroller/newstorelogin`,
	//微信绑定storeId
	bindStoreId: `${host}/szmb/szmbstorecontroller/storeidbindwx`,
	//完善水站信息、
	perfectStore: `${host}/szmb/storeapplyfor/update`,
	perfectStoreFast: `${host}/szmb/storeapplyfor/fastRegister`,
	//查询水站信息
	lookUpStoreInfo: `${host}/szmb/storeapplyfor/selectbyid`,
	//商家登录接口
	shopLogin: `${host}/szmb/szmbstorecontroller/storelogin`,
	storeloginbycode: `${host}/szmb/szmbstorecontroller/storeloginbycode`,
	//分类筛选-查询 - 旧
	lookUpClassify: `${host}/szmb/productclassifymastercontroller/addproductclassify`,

	//分类筛选-查询-新
	lookUpClassify1: `${host}/szmb/productclassifyandbrandcontroller/selectallclassandbrand`,

	//分类筛选-更新
	updateClassify: `${host}/szmb/productclassifymastercontroller/updateproductclassify`,
	//分类筛选-取消勾选
	cancelClassify: `${host}/szmb/productclassifymastercontroller/selectbyclassifyid`,
	//分类筛选-新增分类
	addClassify: `${host}/szmb/szmbserviceset/setclass`,
	//分类删选-删除分类
	delClassify: `${host}/szmb/szmbserviceset/delClass`,
	//分类编辑-查询
	lookUpMineClassify: `${host}/szmb/szmbserviceset/selclass`,

	//商品管理-页面查询
	lookUpShopAdmin: `${host}/szmb/szmstoreshopcontroller/selectallshop`,
	//商品管理-页面查询-另外一个
	lookUpShopAdmin1: `${host}/szmb/szmstoreshopcontroller/selectallstore`,
	//商品管理-页面查询-分页
	lookUpShopAdmin2: `${host}/szmb/szmstoreshopcontroller/selectallshoppage`,
	//商品管理-上架与下架
	shelfState: `${host}/szmb/szmstoreshopcontroller/updateshopstate`,
	//商品管理-推荐与取消推荐
	recommend: `${host}/szmb/szmstoreshopcontroller/updateshoprecommend`,
	//商品管理-品牌分类
	brandClassify: `${host}/szmb/productclassifyandbrandcontroller/selectbyproductclassidandclassid`,
	//商品管理-添加品牌
	addBrandClassify: `${host}/szmb/szmbrandcontroller/insertbrand`,
	//商品管理-品牌关键字查找
	brandkey: `${host}/szmb/productclassifyandbrandcontroller/findbyname`,
	//商品管理-品牌分类提交
	submitBrand: `${host}/szmb/productclassifyandbrandcontroller/addbrands`,
	//商品管理-品牌分类编辑
	editBrand: `${host}/szmb/szmbrandcontroller/updatebrandname`,
	//商品管理-品牌分类删除
	delBrand: `${host}/szmb/szmbrandcontroller/delectbrand`,



	// 不用
	//增加商品-规格-vip价格查询
	lookUpVipPrice: `${host}/szmb/szmbvippricecontroller/selectvipprice`,
	//增加商品-规格-vip价格增加
	addVipPrice: `${host}/szmb/szmbvippricecontroller/inservipprice`,
	//增加商品-规格-vip编辑更新
	editVipPrice: `${host}/szmb/szmbvippricecontroller/updatevipprice`,
	//结束

	//增加商品-规格-vip等级
	lookUpVipLevel: `${host}/szmb/vipvontroller/selectvipall`,
	//新增商品-服务
	addService: `${host}/szmb/szmshopservicecontroller/insertservice`,
	//新增普通商品
	addGoods: `${host}/szmb/szmstoreshopcontroller/insertstoreshop`,
	//编辑普通商品
	editGoods: `${host}/szmb/szmstoreshopcontroller/selectspu`,
	//规格详情查看
	lookUpSpec: `${host}/szmb/szmstoreshopcontroller/selectsku`,
	//查看是否设置vip
	lookSetVip: `${host}/szmb/szmstoreshopcontroller/selectstorevipall`,

	// 我的-用户管理-查询用户信息查询
	lookUpUser: `${host}/szmb/user/selectuser`,
	// 我的-用户管理-新增用户
	addUser: `${host}/szmb/user/insertuser`,
	//我的-用户管理-查看用户详情
	lookUpUserDetail: `${host}/szmb/user/selectone`,
	//我的-用户管理-编辑用户信息
	editUser: `${host}/szmb/user/updatetuser`,
	//vip等级查询
	getVip: `${host}/szmb/vipvontroller/selectvipall`,
	//vip商品列表查询、
	lookUpVipGoods: `${host}/szmb/szmstoreshopcontroller/selectskulist`,
	//查询商品的VIP等级和VIP价格
	lookUpGoodsVip: `${host}/szmb/szmbvippricecontroller/selectskuviplist`,
	//添加商品的VIP等级价格
	addGoodsVip: `${host}/szmb/szmbvippricecontroller/insertskuandvip`,




	//派单管理-派单员管理-查询派单员
	lookUpDriver: `${host}/szmb/szmsendmembercontroller/selectsendmemberbystoreid`,
	//派单管理-派单员管理-新增派单员
	addDriver: `${host}/szmb/szmsendmembercontroller/insertsendmember`,
	//派单管理-派单员管理-查看派单员详细信息
	lookUpDriverDetail: `${host}/szmb/szmsendmembercontroller/selectbystoreanddelivery`,
	selectByStoreId: `${host}/szmb/deliveryusercontroller/selectByStoreId`,
	//派单员管理-派单员详情-派单员扣除
	takeOffDriverMoney: `${host}/szmb/storesendpaycontroller/deductsengmoneyall`,
	//派单员管理-派单员详情-派单员退还
	returnDriverMoney: `${host}/szmb/storesendpaycontroller/storepaydelivery`,



	//派单管理-派单员管理-编辑派单员
	editDriver: `${host}/szmb/szmsendmembercontroller/updatesendmeber`,
	//派单管理-派单员管理-楼层提成查看
	lookUpFloor: `${host}/szmb/szmsendmembercontroller/selectstorefloor`,
	//派单管理-派单员管理-楼层提成编辑
	editFloor: `${host}//szmb/szmsendmembercontroller/updatefloor`,

	//售后管理-查看售后列表
	lookUpSaleList: `${host}/szmb/orderreturn/select`,
	//售后管理-处理售后状态
	dealSaleList: `${host}/szmb/orderreturn/update`,
	//售后管理-退换货详情
	saleGoodsDetail: `${host}/szmb/orderreturn/selectlike`,

	//中心图片库 POST /szmb/imagescontroller/selectalllevel
	// centerImgs: `${host}:10000/szmb/imagescontroller/selectalllevel`,
	centerImgs: `${host}/szmb/imagescontroller/selectalllevel`,

	//套餐申请查询
	lookUpMeal: `${host}/szmb/szmstoreapplycontroller/selectstoreapply`,
	//套餐申请详细信息查询
	lookUpMealDetail: `${host}/szmb/szmstoreapplycontroller/selectapplydeatil`,
	//处理套餐申请
	dealMealApply: `${host}/szmb/szmstoreapplycontroller/updateapplystate`,

	//组合套餐列表
	groupList: `${host}/szmb/szmshopgroupcontroller/selectshopgrouplistall`,
	//修改套餐的上下架
	doUpGroup: `${host}/szmb/szmshopgroupcontroller/updateshopgroupstate`,
	//修改套餐的推荐状态
	doShowGroup: `${host}/szmb/szmshopgroupcontroller/updategroupstate`,
	//组合套餐的列表
	lookUpGroupList: `${host}/szmb/szmshopgroupcontroller/selectskulistall`,
	//组合套餐列表查询关键字
	groupListKey: `${host}/szmb/szmshopgroupcontroller/selectshopgroupname`,
	//添加套餐
	addMeal: `${host}/szmb/szmshopgroupcontroller/insertshopgroup`,
	//编辑套餐
	editMeal: `${host}/szmb/szmshopgroupcontroller/selectshopgroupdeatil`,
	//买赠查询列表
	lookUpSendGoods: `${host}/szmb/szmstoreshopcontroller/selectskulistall`,
	//修改买赠优惠
	changeSendGoods: `${host}/szmb/szmstoreshopcontroller/updateskustate`,


	//积分商城-兑换商品
	lookUpjifenGoods: `${host}/szmb/szmbstoreintegralcontroller/selectstoresku`,
	//积分商城-兑换商品-开启状态
	dealjifenGoods: `${host}/szmb/szmbstoreintegralcontroller/updateintegralsate`,
	//积分商城-兑换水票
	lookUpjifenTicket: `${host}/szmb/szmbstoreintegralcontroller/selectstorewter`,
	//积分商城-兑换税票-状态修改
	dealjifenTicket: `${host}/szmb/szmbstoreintegralcontroller/updatewaterstate`,
	//积分商城-兑换商品-修改积分
	editJifenGoods: `${host}/szmb/szmbstoreintegralcontroller/updateinteferanumber`,
	//积分商城-兑换水票-修改积分
	editJifenTicket: `${host}/szmb/szmbstoreintegralcontroller/updatestorewater`,
	//积分商城-兑换明细-获取列表
	lookUpExchangeList: `${host}/szmb/smzbintegralordercontroller/findallbystoreid`,
	//积分商城-兑换明细-发货
	sendGoods: `${host}/szmb/smzbintegralordercontroller/updateorder`,

	//售后评价-售后评价
	afterSale: `${host}/szmb/orderevaluate/selectafter`,
	//售后评价-商品评价
	afterSaleGoods: `${host}/szmb/orderevaluate/selectbystoreid`,

	//消息管理-选择用户
	selectPeopleList: `${host}/szmb/msg/selectuser`,
	//消息管理-选择用户-关键字搜索
	selectPeople: `${host}/szmb/msg/selectlike`,
	//消息管理-添加消息
	addTidings: `${host}/szmb/msg/insert`,

	//订单设置-查询
	lookUpOrderSet: `${host}/szmb/szmbserviceset/select`,
	//订单设置-保存
	saveOrderSet: `${host}/szmb/szmbserviceset/update`,
	//vip设置-查询
	lookUpVip: `${host}/szmb/vipvontroller/selectvipall`,
	//vip设置-添加
	addVip: `${host}/szmb/vipvontroller/insertvip`,
	//vip设置-修改
	editVip: `${host}/szmb/vipvontroller/updatevipname`,
	//充值设置-查询
	lookUpRecharge: `${host}/szmb/storedeatilcontroller/selectstorerule`,
	//冲值设置-保存
	saveRecharge: `${host}/szmb/storedeatilcontroller/insertstorerule`,

	//安全设置-查询商家账号密码
	lookUpStoreAccount: `${host}/szmb/storedeatilcontroller/selectstoredeatil`,
	//安全设置-修改手机号
	changePhone: `${host}/szmb/updatephone/update`,
	//安全设置-修改密码
	changePassword: `${host}/szmb/storedeatilcontroller/updatestorepassword`,
	//配送规则-查询
	lookUpSendRule: `${host}/szmb/szmshoptitlecontroller/selectstoreshipfig`,
	//配送规则-修改
	changeSendRule: `${host}/szmb/szmshoptitlecontroller/insertstoreshipfig`,
	//广告管理-查询
	lookUpAD: `${host}/szmb/imagescontroller/selectallhome`,
	//广告管理-保存
	saveAD: `${host}/szmb/imagescontroller/updatehome`,
	//头条管理-查询
	lookUpTitle: `${host}/szmb/szmshoptitlecontroller/selecttitle`,
	//头条管理-新增
	addTitle: `${host}/szmb/szmshoptitlecontroller/inserttitle`,
	//头条管理-编辑
	editTitle: `${host}/szmb/szmshoptitlecontroller/selectnews`,
	//头条管理-删除
	delTitle: `${host}/szmb/szmshoptitlecontroller/delectstoretitle`,

	//配送说明-查询记录
	lookUpDriverList: `${host}/szmb/deliveryinfo/select`,
	//配送说明-添加
	addDriverList: `${host}/szmb/deliveryinfo/insertinfo`,
	//配送说明-删除
	deleteDriverList: `${host}/szmb/deliveryinfo/updatstate`,
	//桶押金设置-查询
	lookUpSetBucketMoney: `${host}/szmb/szmbserviceset/selbucket`,
	//桶押金设置
	setBucketMoney: `${host}/szmb/szmbserviceset/setbucket`,
	//保证金查询
	lookUpPromiseMoney: `${host}/szmb/szmbserviceset/selensure`,
	//保证金添加
	addPromiseMoney: `${host}/szmb/szmbserviceset/updateensure`,

	//资产管理-欠款用户信息
	assetsPeple: `${host}/szmb/szmbuserandstorebillcontroller/selectstoreuserall`,
	//资产管理-欠款用户信息详情
	assetsPeopleDetail: `${host}/szmb/szmbuserandstorebillcontroller/selectstoreuserdeatil`,
	assetsPeopleDetailNewApi: `${host}/szmb/productclasscontroller/selectusernewbuckmoney`,
	//资产管理-扣除用户桶押金
	takeOffBucket: `${host}/szmb/storesendpaycontroller/deductUserBuckMoneyAll`,
	//资产管理-查看派单员详细信息-确认还款，还桶
	sureDriverOrderList: `${host}/szmb/szmbuserandstorebillcontroller/storeaffrom`,

	//水票管理-查看商家水票信息
	lookUpWaterTicket: `${host}/szmb/szmshopwatercontroller/selctshopwater`,
	//水票管理-查看商家商品
	lookUpStoreGoods: `${host}/szmb/szmshopwatercontroller/selectskustore`,
	//税票管理-添加水票
	addWaterTicket: `${host}/szmb/szmshopwatercontroller/inserwater`,
	//水票管理-编辑库存
	editWaterTicketStock: `${host}/szmb/szmshopwatercontroller/updatewater`,
    //赠送水票
	sendWaterCoupon: `${host}/szmb/szmshopwatercontroller/sendWaterCoupon`,


	//服务订单-查询服务列表
	lookUpServiceList: `${host}/szmb/serviceorder/selectall`,
	//服务订单-查询服务评价
	lookUpServiceComment: `${host}/szmb/serviceorder/selectbystoreId`,
	//服务订单-处理服务订单
	dealServiceList: `${host}/szmb/serviceorder/update`,
	//服务订单-订单详情
	serviceOrderDetail: `${host}/szmb/serviceorder/selectone`,

	//正式版-查询
	lookUpMoney: `${host}/szmb/rule/selectstoreidnew`,
	//正式版-充值
	toRecharge: `${host}/api/rechargeable/wxPay-B`,
	// 测试版App 充值
	appToRecharge: `${host}/api/rechargeable/wxPay-B2`,
	//我的-试营业倒计时
	meTimeOut: `${host}/szmb/storeapplyfor/selectbystoreid`,
	//我的-店铺信息
	lookUpStore: `${host}/szmb/storeapplyfor/selectbyid`,

	//全局新订单查询
	lookUpNewOrder: `${host}/szmb/szmborder/selectremind`,

	//我的-服务订单数量查询  暂时不用
	lookUpMineServiceNum: `${host}/szmb/serviceorder/count`,
	//我的-红点数量查询
	lookUpMineRedNum: `${host}/szmb/szmbvippricecontroller/selectstorenumber`,

	//app-清除登录状态
	removeLoginState: `${host}/szmb/szmbstorecontroller/exit`,

	//手填订单-添加
	addHandOrder: `${host}/szmb/szmborderstorecontroller/addorder`,
	//手填订单列表查询
	lookUpHandOrder: `${host}/szmb/szmborderstorecontroller/selectorderbystoreid`,
	//查看手填订单详情
	lookUpHandOrderDetail: `${host}/szmb/szmborderstorecontroller/selectbyordernum`,
	//修改手填订单
	changeHandOrder: `${host}/szmb/szmborderstorecontroller/updateorder`,

	//短信管理页面
	messageAdmin: `${host}/szmb/szmbsms/select`,
	//短信充值页面展示
	showMessage: `${host}/szmb/szmbsms/selectprice`,
	//短信充值
	rechargeMessage: `${host}/api/rechargeable/wxPay-Sms`,
	// app 短信充值
	appPayMessage: `${host}/api/rechargeable/wxPay-Sms2`,
	// api/rechargeable/wxPay-Sms2//购买短信
	// api/rechargeable/wxPay-B2//充值
	//查看固定派单员
	lookOfficialWorker: `${host}/szmb/deliveryusercontroller/selectsenddeletestate`,
	//绑定固定派单员
	bindOfficialWorker: `${host}/szmb/deliveryusercontroller/bingduser`,
	//关键字搜索固定派单员
	toSearchOfficialWorker: `${host}/szmb/deliveryusercontroller/selectdeliveryusername`,

	// lcj 2020-05-26
	// 借还列表查询
	selectusergoodsdeatil: `${host}/szmb/newuseryfcontroller/selectusergoodsdeatil`,

	//查询借物管理
	lookUpBorrowBucket: `${host}/szmb/szmbserviceset/bandrlist`,
	//借物管理-同意拒绝
	checkBorrowBucket: `${host}/szmb/szmbserviceset/borrow`,
	// 借物管理 确认送达
	deliveryconfirm: `${host}/szmb/szmstoreapplycontroller/deliveryconfirm`,
	//还物管理-同意拒绝
	checkReturnBucket: `${host}/szmb/szmbserviceset/repay`,
	// 还物 确认回收
	// deliveryquern: `${host}/szmb/szmstoreapplycontroller/deliveryquern`,

	//还桶/还物-指派
	returnBindWorker: `${host}/szmb/szmstoreapplycontroller/updatedesinage`,
	//还东西-查看固定派单员
	lookUpReturnOfficialPeople: `${host}/szmb/szmsendmembercontroller/selectdelectstate`,
	//还东西-搜索固定派单员
	toSearchReturnOfficialPeople: `${host}/szmb/szmsendmembercontroller/selectdeliveryname`,

	//新增商品-查询商品分类
	lookUpAddGoodsClassify: `${host}/szmb/szmbrandcontroller/selectallclassandbrand`,
	//新增商品-查询商品分类下的品牌
	lookUpAddGoodsBrands: `${host}/szmb/szmbrandcontroller/selectallbrand`,

	//资产管理-汇总-借物未还详情
	lookUpCollectInfo: `${host}/szmb/borrowcontroller/selectallbyuserid`,
	//资产管理-汇总-压桶未退，欠款未还详情
	lookUpCollectReturnBucketInfo: `${host}/szmb/szmbuserandstorebillcontroller/selectstorealllist`,

	//用户绑定优惠价格
	bindDiscount: `${host}/szmb/szmbvippricecontroller/selectdiscounts`,
	//查询用户绑定优惠
	lookUpBindDiscount: `${host}/szmb/szmbvippricecontroller/selectuserproduct`,

	//借物添加备注
	borrowAddRemarks: `${host}/szmb/szmstoreapplycontroller/updateremark`,





	// 2019-06-10 产品库
	//查看全部分类下的全部商品
	lookUpAllProducts: `${host}/szmb/newinsertproductcontroller/selectstoreproduct`,
	// 全部分类查询
	lookUpAllClassify: `${host}/szmb/newclasscontroller/selectclass`,
	//产品库上架
	upShelfProduct: `${host}/szmb/newinsertproductcontroller/insertproductlist`,
	//产品库新增商品
	addProductLibrary: `${host}/szmb/szmstoreshopcontroller/insertstoreshop`,
	// 新增商品
	insertProduct: `${host}/szmb/newinsertproductcontroller/insertproduct`,
	// 更新提成信息
	updateticheng: `${host}/szmb/szmstoreshopcontroller/updateticheng`,
	//产品库搜索商品
	searchProductLibrary: `${host}/szmb/newclasscontroller/selectproductall`,

	//库存预警-查询分类列表
	lookUpStoreClassify: `${host}/szmb/productclasscontroller/selectclass`,
	//修改库存预警值
	changeStoreWarning: `${host}/szmb/productclasscontroller/updateclassnumber`,
	//删除组合商品
	delGroupGoods: `${host}/szmb/szmshopgroupcontroller/delectshopgroup`,
	//客户详情
	lookUpCostomerDetail: `${host}/szmb/productclasscontroller/selectuserdeatil`,
	//查看派单员详细提成
	lookUpDriverMoneyDetail: `${host}/szmb/szmsendmembercontroller/selectdeliverydetail`,
	//编辑水票查询详细信息
	lookUpWaterDetailInfo: `${host}/szmb/szmshopwatercontroller/selectwater`,
	//编辑水票信息
	changeWaterInfo: `${host}/szmb/szmshopwatercontroller/updatewaterall`,


	//派单员提成
	deliverMoneyDetail: `${host}/szmb/szmsendmembercontroller/selectdeliverydetail`,
	//派单员提成分析汇总
	selectdeliverydetailanalysis: `${host}/szmb/szmsendmembercontroller/selectdeliverydetailanalysis`,
	//派单员商品汇总明细
	selectdeliverydetailproduct: `${host}/szmb/szmsendmembercontroller/selectdeliverydetailproduct`,
	//派单员结算
	accountDelivery: `${host}/szmb/deliveryusercontroller/clearmoney`,
	//派单员订单查询
	lookUpDeliverOrder: `${host}/szmb/szmsendmembercontroller/selectdeliverystoreoptimizt`,


	// 查看客户订单
	lookUpCustomerOrder: `${host}/szmb/user/selectuserordercollect`,

	//积分开关查询接口
	lookUpSendJifen: `${host}/szmb/szmbserviceset/isopen`,

	// 积分赠送提交
	sendJifenSubmit: `${host}/szmb/szmbstorecontroller/addintegral`,

	//查看商家的所有品牌
	lookUpStoreAllBrand: `${host}/szmb/productclasscontroller/selectstorebrand`,


	//按照条件查找利润
	searchToLookMoney: `${host}/szmb/szmstorebillcontroller/selectproductall`,
	//查看收支简表详情
	lookUpGetSendMoneyDetail: `${host}/szmb/szmstorebillcontroller/selectskualllist`,


	// 水票商品查看
	lookUpWaterGoods: `${host}/szmb/szmstoreshopcontroller/newselectclasssku`,
	//设置商品参与水票活动
	setGoodsToWater: `${host}/szmb/szmshopwatercontroller/updatewaterstate`,

	//进货调整，盘点库存
	lookUpMyProcuct: `${host}/szmb/newclasscontroller/selectproductlistinventory`,
	//进货调整，搜索
	searchMyProduct: `${host}/szmb/newclasscontroller/selectlistproductname`,
	//进货调整，盘点库存提交
	submitMyProductStock: `${host}/szmb/szmstoreshopcontroller/updateskulist`,


	// 查看压桶品牌
	lookUpBucketBrand: `${host}/szmb/szmbuserandstorebillcontroller/selectstorebuck`,
	//修改桶押金
	changeBucketPrice: `${host}/szmb/szmbuserandstorebillcontroller/insertstorebuck`,

	// 定位设置-查询
	lookUpLocationSet: `${host}/szmb/szmbserviceset/sellocation`,
	//定位设置-修改
	changeLocationSet: `${host}/szmb/szmbserviceset/location`,

	//资产详情-查看月付
	lookUpMonthPay: `${host}/szmb/szmborder/paymonthly`,

	//清洗服务操作接口
	cleanOperate: `${host}/szmb/serviceorder/state`,

	//客户管理-编辑-借物开关
	jwSwitch: `${host}/szmb/user/borrowthings`,

	//资产管理-提现-查询'
	lookUpCanGetMoney: `${host}/szmb/szmbstorewallet/daydeposit`,

	//客户管理-编辑-月付开关
	yfSwitch: `${host}/szmb/user/paymonthlyopen`,

	//客户管理-列表搜索
	searchCustomerList: `${host}/szmb/user/selectuserbyconditionNew`,

	//首页-充值弹窗提示-查询金额
	lookUpRechargeMoney: `${host}/szmb/rule/selectprimary`,


	// 空桶盘点
	// 查看水桶
	lookUpWaterBucket: `${host}/szmb/inventory/selectbuck`,
	// 查看空桶
	lookUpEmptyBucket: `${host}/szmb/inventory/selectemptybuck`,
	//空桶盘点保存
	saveEmptyBucket: `${host}/szmb/inventory/updateemptybuck`,
	//查询空桶品牌
	lookUpEmptyBucketBrand: `${host}/szmb/inventory/selbrand`,
	//新进空桶
	addNewEmptyBucket: `${host}/szmb/inventory/insertemptybuckrecord`,
	// 新进空桶记录
	addNewEmptyBucketRecord: `${host}/szmb/inventory/selemptybuckrecord`,


	// 水厂采购

	//查询商铺绑定水厂
	storeBindWaterWorksList: `${host}/water/waterstoreController/selectAllByStoreId`,
	//解除商铺绑定水厂
	breakBindWaterWorks: `${host}/water/waterstoreController/updateByStoreId`,
	//确认解除绑定关系
	makeSureBreakBind: `${host}/water/waterstoreController/unbundle`,
	//厂家全部商品
	waterWorksGoods: `${host}/water/waterproductcontroller/selectwaterskulistbywaterid`,
	//水厂采购-新增订单
	waterWorksAddNewOrder: `${host}/water/WaterorderstoreController/addorder`,
	//一键历史订单
	waterWorksAgoOrder: `${host}/water/waterstoreController/historyOrder`,
	//查看水站采购单
	lookUpStoreBuyOrderList: `${host}/water/WaterorderstoreController/selectbystoreid`,
	//水站采购单-取消
	cancelStoreOrder: `${host}/water/WaterorderstoreController/cancelorder`,
	//水站采购单-去支付
	payOrderStoreOrder: `${host}/water/WaterorderstoreController/payorder`,
	//水站采购单-修改订单状态
	chagneStoreOrder: `${host}/water/WaterorderstoreController/updateorder`,
	// //查看水站订单详情
	lookUpStoreOrderDetail: `${host}/water/WaterorderstoreController/selectbyordernum`,
	// //查看水站订单物流详情
	lookUpStoreOrderDriverDetail: `${host}/water/WaterorderstoreController/checkLogistics`,
	// //查看派单员经纬度
	lookUpWaterDriverLoclog: `${host}/water/WaterorderstoreController/selectlongitude`,
	//水厂采购-派单详情-催单
	urgeStoreOrder: `${host}/water/WaterorderstoreController/urgeOrder`,
	//查看历史交易数据
	lookUpAgoBusinessData: `${host}/water/waterbrandcontroller/selectwaterbill`,
	//查看水厂品牌信息
	lookUpWaterWorksBrands: `${host}/water/waterbrandcontroller/selectwaterbrandlist`,
	//查看历史交易数据详细信息
	lookUpAgoBusinessDetail: `${host}/water/waterbrandcontroller/selectskubilllist`,
	//水厂-回桶-押桶记录
	lookUpWaterWorksPledBucket: `${host}/water/waterpledgcontroller/selectstorebuckAlllist`,
	//水厂-回桶-欠桶记录
	lookUpWaterWorksOweBucket: `${host}/water/waterdebtbackcontroller/selectdebtandreturnbucklist`,
	//水厂-回桶-回桶记录1
	lookUpWaterWorksBackBucket1: `${host}/water/waterdebtbackcontroller/selectduocountlist`,
	//水厂-回桶-回桶记录2
	lookUpWaterWorksBackBucket2: `${host}/water/waterdebtbackcontroller/selectstorerepaylist`,
	//水厂-回桶-确认
	makeSureBackBucket: `${host}/water/waterdebtbackcontroller/storeaffrim`,
	//水厂-回桶-退桶
	waterWorksBackBucket: `${host}/water/waterpledgcontroller/insertstorebuck`,
	//水厂-回桶-还桶
	waterWorksReturnBucket: `${host}/water/waterdebtbackcontroller/insertRetrunback`,
	//水厂-回桶-角标
	waterWorksReturnBucketBadge: `${host}/water/waterdebtbackcontroller/selectcornoer`,
	//水厂-水站绑定
	storeBindWaterWorks: `${host}/water/waterworkscontroller/bindStoreId`,
	// 商品详情
	waterWorksGoodsDetail: `${host}/water/waterproductcontroller/selectwaterspu`,
	//月结款项汇总
	waterWorksPaymentTotal: `${host}/water/waterordercontroller/selectfundmoney`,
	//月结款项记录
	waterWorksPaymentList: `${host}/water/waterordercontroller/selectwaterfundlist`,
	//月结款项确认
	waterWorksPaymentSure: `${host}/water/waterordercontroller/updatewaterfund`,
	//查看月结订单
	waterWorksMonthOrder: `${host}/water/waterordercontroller/selectwaterorderlist`,
	//水厂消息
	waterWorksTidings: `${host}/water/watermessagecontroller/selectstoremessagetlist`,
	//水厂消息已读
	waterWorksTidingsRead: `${host}/water/watermessagecontroller/updatemessage`,

	//生成我的名片
	getMyCard: `${host}/api/excal/selecttoke`,

	//地址查询
	lookUpAddress: `${host}/dpt/address/business`,

	// start by yanxingwang 2019-12-05
	// 删除消息
	delectmessage: `${host}/water/watermessagecontroller/delectmessage`,

	// 设置商品优惠价格和水票买赠规则
	updatediscount: `${host}/szmb/user/updatediscount`,

	// 移除商品优惠价格和水票买赠规则
	deldiscount: `${host}/szmb/user/deldiscount`,

	// 查询设置优惠价格列表
	userwaterDiscountList: `${host}/szmb/user/userwater`,

	//功能模块蒙版引导查询
	lookUpMarkGuide: `${host}/szmb/szmbmaskcontroller/selectallbystoreid`,
	//蒙版引导查询
	closeMarkGuide: `${host}/szmb/szmbmaskcontroller/updatemaskbyid`,
	//蒙版引导总开关修改
	changeAllOfMarkGuide: `${host}/szmb/szmbmaskcontroller/updatemaskbystoreid`,

	// 查询与水站绑定的用户列表(App)
	selectuserapp: `${host}/szmb/msg/selectuserapp`,
	selectuserapplist: `${host}/szmb/customer/pageList`,

	// 修改选中状态(App)
	updateuserpitch: `${host}/szmb/msg/updateuserpitch`,

	// 退出页面重置,选择状态
	updateUserState: `${host}/szmb/msg/updatestate`,

	// 消息-单发-添加消息、短信(APP)
	insertmsgapp: `${host}/szmb/msg/insertmsgapp`,

	// 获取消息模板
	getMsgTemplateByMerchantId: `${host}/szmb/msgTemplate/getMsgTemplateByMerchantId`,

	// 编辑消息模板
	updateMsgTemplate: `${host}/szmb/msgTemplate/updateMsgTemplate`,

	// 删除消息模板
	offlineMsgTemplate: `${host}/szmb/msgTemplate/offlineMsgTemplate`,

	// 确认收款
	updatepledgmoneystate: `${host}/szmb/newdebtbuckcontroller/updatepledgmoneystate`,

	// 指派派单员  退桶操作，确认退款，同意，拒绝
	updatebuckmoneystate: `${host}/szmb/newdebtbuckcontroller/updatebuckmoneystate`,

	// 新用户，权限开关list为0时，调用该接口
	loginaddswitch: `${host}/szmb/user/loginaddswitch`,

	// 登录检测
	crushlogin: `${host}/szmb/szmbstorecontroller/crushlogin`,

	// 增强版登录检测（支持多平台并发登录）
	crushloginEnhanced: `${host}/szmb/szmbstorecontroller/crushlogin-enhanced`,

	// 派单员手填订单详情
	selectdeliveryorderdeatil: `${host}/szmb/deliveryordercontroller/selectdeliveryorderdeatil`,

	// 删除商品信息
	delectproduct: `${host}/szmb/szmstoreshopcontroller/delectproduct`,

	// 语音提醒接口
	voiceremind: `${host}/szmb/szmbstorecontroller/voiceremind`,

	// 商品管理商品信息
	selectstoreshopclassone: `${host}/szmb/szmstoreshopcontroller/selectstoreshopclassone`,
	selectstoreshopclassonelianying: `${host}/szmb/szmstoreshopcontroller/selectstoreshopclassonelianying`,
	selectstoreshopclassonedikou: `${host}/szmb/szmstoreshopcontroller/selectstoreshopclassonedikou`,
	selectstoreshopclasscommon: `${host}/szmb/szmstoreshopcontroller/selectstoreshopclasscommon`,
	selectstoreshopclassdrainage: `${host}/szmb/szmstoreshopcontroller/selectstoreshopclassdrainage`,

	// 商品管理商品信息
	selectstoreshopclassdelivery: `${host}/szmb/szmstoreshopcontroller/selectstoreshopclassdelivery`,
	selectstoreshopclassyewu: `${host}/szmb/szmstoreshopcontroller/selectstoreshopclassyewu`,

	// 撤销押桶
	updatedeliveryinfo: `${host}/szmb/newdebtbuckcontroller/updatedeliveryinfo`,

	// 分享H5
	shareH5toruzhu:`${host}/api/excal/selectstoretostore`,

	// new 客户管理
	newUserListApi:`${host}/szmb/user/selectuserbyconditionmove`,

	// 获取分类列表
	classifyListApi:`${host}/szmcproductclassifycontroller/getClassifyList`,

	// 新增分类名
	addNewclassifyApi:`${host}/szmcproductclassifycontroller/addProductClassifyName`,

	// 删除分类名
	deleteclassifyApi:`${host}/szmcproductclassifycontroller/deleteProductClassifyName`,

	// 修改分类名
	editclassifyApi:`${host}/szmcproductclassifycontroller/editOptionClassify`,

	// 客户详情new
	newUserDetailApi:`${host}/szmb/user/selectuserhomepropertycollect`,

	// 客户详情水票
	newUserwaterApi:`${host}/szmb/water/seluserwatercouponrecord`,

	// 客户详情水票总金额
	newWaterMoneyApi:`${host}/szmb/water/seluserwatercouponnumber`,

	// 客户详情失效水票
	newLoseWaterApi:`${host}/szmb/water/selwaterloserecord`,

	// 客户详情水票确认到账
	dongjie:`${host}/szmb/water/dongjie`,
	
	newaffirbankWaterApi:`${host}/szmb/water/affirbank`,
	newaffirbankWateronlyApi:`${host}/szmb/water/affirbankonly`,
	

	// 客户详情水票确认退款
	newLoseAfirmApi:`${host}/szmb/water/confirmrefund`,
	waterlist:`${host}/szmb/water/list`,

	// 客户详情回桶欠桶
	newBackBucketApi:`${host}/szmb/newdebtbuckcontroller/selectdebtlist1`,

	// 客户详情回桶欠桶确认收款
	newBucketshoukuanApi:`${host}/szmb/newdebtbuckcontroller/updatestoremoneybuck`,

	// 客户详情押桶
	newPledgBucketApi:`${host}/szmb/newdebtbuckcontroller/selectpledglist`,

	// 客户详情押桶 确认收款
	newPledgCorfirmGetmoneyApi:`${host}/szmb/newdebtbuckcontroller/updatepledgmoneystate`,


	// 客户详情退桶
	newBackBarrelApi:`${host}/szmb/newdebtbuckcontroller/selectbucketlist`,

	// 客户详情退桶确认退款
	newBackMoneyApi:`${host}/szmb/newdebtbuckcontroller/updatebuckmoneystate`,

	// 客户详情押桶剩余未退汇总
	newSurplusNoReturnApi:`${host}/szmb/newdebtbuckcontroller/selectuserpledgelistall`,


	// 客户详情汇总欠桶
	newQianTongTotalApi:`${host}/szmb/newdebtbuckcontroller/selectusercollect`,
	newdebtbuckcontrollerselectStoreCollect:`${host}/szmb/newdebtbuckcontroller/selectStoreCollect`,

	// 回桶 -0----
	newHuiTongtttApi:`${host}/szmb/newdebtbuckcontroller/selectuserdebtlist`,
	// 还桶查看
	newHuantongClassApi:`${host}/api/debtbuckcontroller/selectstorebrandlist`,

	// 合作结束还桶提交
	newHuiTongSubApi:`${host}/szmb/newdebtbuckcontroller/insertrepaybuck`,

	// 退桶查看替换品牌
	newTuiTBrandApi:`${host}/api/debtbuckcontroller/selectstorebrandlist`,

	// 退桶 提交
	newTuiTSubmitApi:`${host}/szmb/newdebtbuckcontroller/insertbucketbuck`,

	//客户资产 回 押 退 角标
	newUserZCbadgeApi:`${host}/szmb/user/selectuserassetmarkcollect`,

	// 回桶指派
	newbackBucketZhiPApi:`${host}/szmb/newdebtbuckcontroller/updatebuckmoneystate`,

	// 派单员列表
	newSinglePeopleApi:`${host}/szmb/szmsendmembercontroller/selectdelectstate2`,

	// 确认回收
	newComfirHuishouApi:`${host}/szmb/szmstoreapplycontroller/deliveryquern`,

	// 物资管理详情 确认送达 csl
	newWuZhiSDApi:`${host}/szmb/szmstoreapplycontroller/deliveryconfirm`,

	// 物资管理 返还添加备注
	newWuZhiremarkApi:`${host}/szmb/newuseryfcontroller/updateobject`,

	// 物资管理回桶欠桶
	newNewBackBucketApi:`${host}/szmb/newdebtbuckcontroller/selectBack`,

	// 客户总角标
	newUserAllBadgeApi:`${host}/szmb/user/selectuseradminmarkcollect`,

	// 查看客户基本信息
	newUserInfosDeApi:`${host}/szmb/user/selectbasicinfo`,

	// 图片验证码
	newImgCodeApi:`${host}/szmb/code/getImgCode`,

	// 新增商品优惠 移除
	newRemoveUpriceApi:`${host}/szmb/user/removePreferentialPrice`,
	
	newSendMsgCodeApi:`${host}/szmcuercontroller/sendsmsverificationcode`,
	
	// 投诉/建议
	suggestionApi: `${host}/szmcfeedback/insert`,
	
	//创建转单订单
	exchangeOrderCreateApi:`${host3}/storeOrder/saveStoreOrder`,
	//转单支付
	exchangeOrderCreatePayApi:`${host3}/storeOrder/payConfirmStoreOrder4TQFK`,
	//转单列表
	exchangeOrderListApi:`${host3}/storeOrder/listStoreOrder`,
	//转单抢单
	exchangeOrderTakeOrderApi:`${host3}/storeOrder/takeStoreOrder`,
	// 转单绑定商品
	connectGoods:`${host3}/storeOrder/connectGoods`,
	//我的商品查询
	exchangeOrderProductListApi:`${host3}/storeOrder/listProduct`,
	//抢单支付
	exchangeOrderTakePayApi:`${host3}/storeOrder/payConfirmStoreOrder4HDFK`,
	//转单重新支付
	exchangeOrderRepayApi:`${host3}/storeOrder/repayStoreOrder`,
	//查询资产冻结单
	frozenOrderListApi:`${host3}/frozenOrder/getFrozenAssetOrder`,
	//生成资产冻结单
	frozenOrderFreezeApi:`${host3}/frozenOrder/freezeAsset`,
	//资产冻结单结算
	frozenOrderUpdateStatusApi:`${host3}/frozenOrder/updateOrderStatus`,
	//查询订单转单状态
	queryOrderExchangeStatusApi:`${host3}/storeOrder/listStatusByMainOrder`,
	//取消转单
	cancelStoreOrderJustUpdate:`${host3}/storeOrder/cancelStoreOrderJustUpdate`,
	// 接单，不支付 API
	jiedan:`${host3}/storeOrder/jiedan`,
	
	cuidanlist: `${host}/szmb/szmborder/cuidanlist`,
	companyList: `${host}/company/findByStoreId`,
	deliveryproductfindByDeliveryUserIdAndProductId: `${host}/deliveryproduct/findByDeliveryUserIdAndProductId`,
	commercialproductfindByCommercialDelegateIdAndProductId: `${host}/commercialproduct/findByCommercialDelegateIdAndProductId`,
	deliveryproductdelete: `${host}/deliveryproduct/delete`,
	deliveryproductsave: `${host}/deliveryproduct/save`,
	commercialproductsave: `${host}/commercialproduct/save`,
	ticketinsertticket: `${host}/ticket/insertticket`,
	ticketfindByTypeAndStoreId: `${host}/ticket/findByTypeAndStoreId`,
	ticketinsertxinrenticket: `${host}/ticket/insertxinrenticket`,
	ticketinfo: `${host}/ticket/info`,
	ticketupdateticket: `${host}/ticket/updateticket`,
	ticketdelete: `${host}/ticket/delete`,
	selectallbrandbystore: `${host}/szmb/newclasscontroller/selectbrand`,
	commercialdelegatecontrollerselectList: `${host}/szmc/commercialdelegatecontroller/selectList`,
	// 查看回桶单
	selectuserrepaylist: `${host}/szmb/newdebtbuckcontroller/selectuserrepaylist1`,
	// 查看押桶记录
	selectpledglist: `${host}/szmb/newdebtbuckcontroller/selectpledglist1`,
	// 查看退桶记录
	selectbucketlist: `${host}/szmb/newdebtbuckcontroller/selectbucketlist1`,
	// c端查看押桶申请列表
	selectproductlistall: `${host}/szmb/newdebtbuckcontroller/selectproductlistall1`,
	szmcstoreinfo: `${host}/szmcstore/infoResult`,
	szmcstoreupdate: `${host}/szmcstore/update`,
	szmcstoreupdateminnumber: `${host}/szmcstore/updateminnumber`,
	invoicebcontrollerselectbystoreid: `${host}/szmb/invoicebcontroller/selectbystoreid`,
	invoicebcontrollerupdateinvoice: `${host}/szmb/invoicebcontroller/updateinvoice`,
	invoiceordercontrollerselectbyid: `${host}/szmc/invoiceordercontroller/selectbyid`,
	invoicebcontrollerselectorderlistbyinvoiceid: `${host}/szmb/invoicebcontroller/selectorderlistbyinvoiceid`,
	smzcwcuserefundConfirm: `${host}/smzcwcuse/refundConfirm`,
	smzcwcuserefunddel: `${host}/smzcwcuse/smzcwcuserefunddel`,
	selectsendmemberbystoreidresult: `${host}/szmb/szmsendmembercontroller/selectsendmemberbystoreidresult`,
	newUserupdateUserYf: `${host}/newUser/updateUserYf`,
	szmcstorefindByState: `${host}/szmcstore/findByState`,
	szmsendmembercontrollerback: `${host}/szmb/szmsendmembercontroller/back`,
	szmsendmembercontrollerbackAdmin: `${host}/szmb/szmsendmembercontroller/backAdmin`,
	deliveryordercontrollerselectbystoreid: `${host}/szmb/deliveryordercontroller/selectbystoreid`,
	deliveryordercontrollerselectbyuserid: `${host}/szmb/deliveryordercontroller/selectbyuserid`,
	lianyinglist: `${host}/lianying/list`,
	lianyingupdatestate: `${host}/lianying/updatestate`,
	lianyingupdatetuijian: `${host}/lianying/updatetuijian`,
	drainagefindByStoreId: `${host}/drainage/findByStoreId`,
	drainageinfo: `${host}/drainage/info`,
	drainageinsert: `${host}/drainage/insert`,
	drainageupdate: `${host}/drainage/update`,
	drainagedelete: `${host}/drainage/delete`,
	drainageUserfindTuanzhang: `${host}/drainageUser/findTuanzhang`,
	newUseraddTuanzhang: `${host}/newUser/addTuanzhang`,
	drainageUserfindDrainageOrder: `${host}/drainageUser/findDrainageOrder`,
	drainagegetShareResultAdmin: `${host}/drainage/getShareResultAdmin`,
	productTypefindByStoreId: `${host}/productType/findByStoreId`,
	szmcstoreselectallstore: `${host}/szmcstore/selectallstore`,
	refreshPhone: `${host}/jddj/refreshPhone`,
	uploadPic: `${host}/uploadcontroller/upload`,
	updatePirurlByOrderId: `${host}/szmb/szmsendmembercontroller/updatePirurlByOrderId`,
	lookUpAllBrand: `${host}/api/debtbuckcontroller/selectstorebrandlist`,
	submitBucketOrder: `${host}/szmb/newdebtbuckcontroller/insertrepaybuck`,
	selectusercollect: `${host}/szmb/newdebtbuckcontroller/selectusercollect`,
	clearBackOrder: `${host}/api/debtbuckcontroller/delectordernumber`,
	selectorderonebyordernum: `${host}/szmb/szmborder/selectorderonebyordernum`,
	configfindbylabel: `${host}/config/findByLabel`,
	transferCreate: `${host}/transfer/create`,
	transferList: `${host}/transfer/list`,
	transferCancel: `${host}/transfer/cancel`,
	transferBack: `${host}/transfer/back`,
	transferfrontList: `${host}/transfer/frontList`,
	transferVerifyCount: `${host}/transfer/verifyCount`,
	transferVerify: `${host}/transfer/verify`,
	transferfrontCount: `${host}/transfer/frontCount`,
	transferTake: `${host}/transfer/take`,
	// 派单员新增订单 POST
	insertdeliveryorder: `${host}/szmb/deliveryordercontroller/insertdeliveryorder`,
	szmcstoreupdateShoukuanma: `${host}/szmcstore/updateShoukuanma`,
	submitPledgeBucket: `${host}/szmb/newdebtbuckcontroller/submitPledgeBucket`,
	sumuserorder: `${host}/szmb/deliveryordercontroller/sumuserorder`,
	// 查询所有分类
	classifyListApi: `${host}/szmb/productclassifycontroller/selectallclassify`,
	// 新增分类
	addNewclassifyApi: `${host}/szmb/productclassifycontroller/insertclassify`,
	// 编辑分类
	editclassifyApi: `${host}/szmb/productclassifycontroller/updateclassify`,
	// 删除分类
	deleteclassifyApi: `${host}/szmb/productclassifycontroller/deleteclassify`,
	// 查询所有品牌
	selectallbrandbystore: `${host}/szmb/szmbrandcontroller/selectallbrandbystore`,
	// 新增商品
	insertProduct: `${host}/szmb/newinsertproductcontroller/insertproduct`,
	// 产品管理-分类查询
	selectClass: `${host}/szmb/newclasscontroller/selectclass`,
	// 产品管理-分类新增
	insertClass: `${host}/szmb/newclasscontroller/insertclass`,
	// 产品管理-分类更新
	updateClass: `${host}/szmb/newclasscontroller/updateclass`,
	// 产品管理-分类删除
	deleteClass: `${host}/szmb/newclasscontroller/deleteclass`,

	// 产品管理-品牌查询
	selectBrand: `${host}/szmb/newclasscontroller/selectbrand`,
	// 产品管理-品牌新增
	addBrand: `${host}/szmb/newclasscontroller/addbrand`,
	// 产品管理-品牌更新
	updateBrand: `${host}/szmb/newclasscontroller/updatebrand`,
	// 产品管理-品牌删除
	deleteBrand: `${host}/szmb/newclasscontroller/deletebrand`,
	// 产品管理-品牌查询单个
	selectBrandById: `${host}/szmb/newclasscontroller/selectbrandbyid`,
	selectByPhoneAndStoreId: `${host}/newUser/selectByPhoneAndStoreId`,
	selectOneUserreturnAdmin: `${host}/szmcordermaincontroller/selectOneUserreturnAdmin`,
	addressgetdeliveryuser: `${host}/szmb/deliveryusercontroller/addressgetdeliveryuser`,
	// 配送员统计明细
	selectDeliverycountstatistics: `${host}/szmb/szmsendmembercontroller/selectDeliverycountstatistics`,
	geocodereverse: `${host}//util/geocode/reverse`,

	// 订单分析管理相关接口
	// 查询所有商家
	selectallstore: `${host}/szmcstore/selectallstore`,
	// 订单分析列表查询
	findallorderall: `${host}/szmcordermaincontroller/findallorderall`,
	// 获取超时统计
	getTimeoutStats: `${host}/szmcordermaincontroller/getTimeoutStats`,
	// 获取未送达超时统计
	getUndeliveredTimeoutStats: `${host}/szmcordermaincontroller/getUndeliveredTimeoutStats`,
	// 批量更新订单标记
	updatebatchmark: `${host}/szmcordermaincontroller/updatebatchmark`,
	// 更新单个订单标记
	updatemark: `${host}/szmcordermaincontroller/updatemark`,
	// 更换订单商家
	updatestore: `${host}/szmcordermaincontroller/updatestore`,
}

module.exports = config;
