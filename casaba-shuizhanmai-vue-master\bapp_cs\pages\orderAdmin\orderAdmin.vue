<template>
	<view>
		<view class="content">
			<view class="headBox" style="padding-bottom:0;">
				<view>
					<scroll-view class="navBarBox"  scroll-x
						:scroll-left="scrollLeft" :scroll-with-animation="true">
						<view class="barBox" v-for="(item, index) in switchtab"
							:class="currentTab == index ? 'select' : 'default'" :key="item.name" @click.stop="switchNav"
							:data-current="index" :data-id="item.classId">
							{{ item.name }}
							<text v-if="badgeList && badgeList.daiPaiDan && index == 0" style="color: #ff2f2f">({{
								badgeList.daiPaiDan }})</text>
							<text v-if="badgeList && badgeList.tranCount && index == 1" style="color: #ff2f2f">({{
								badgeList.tranCount }})</text>
							<text v-if="badgeList && badgeList.daiJieDan && index == 2" style="color: #ff2f2f">({{
								badgeList.daiJieDan }})</text>
							<text v-if="badgeList && badgeList.huitui && index == 4" style="color: #ff2f2f">({{
								badgeList.huitui }})</text>
							<text v-if="badgeList && badgeList.selfCount && index == 3" style="color: #ff2f2f">({{
								badgeList.selfCount }})</text>
							<text v-if="badgeList && badgeList.pledgeBuckApplyNum && index == 6" style="color: #ff2f2f">({{
								badgeList.pledgeBuckApplyNum }})</text>
							<view class="a"></view>
						</view>
					</scroll-view>
				</view>
			</view>
			<!--头部tab切换部分start-->
			<swiper :current="currentTab" @change="tabChange" class="mySwiper">

				<!-- 水站待派单 -->
				<swiper-item class="position-relative">
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">
						<view @click="refreshData" class="lianying">
							<view>刷新</view>
							<view>数据</view>
						</view>
						<!-- 搜索框和排序按钮 -->
						<view class="search-sort-container">
							<view class="search-box">
								<input type="text" v-model="searchName" placeholder="请输入关键词搜索" @input="handleSearch" />
								<view class="search-btn" @click="doSearch">
									<text>搜索</text>
								</view>
							</view>
							<view :class="sortOrder === 'desc' ? 'sort-box-desc' : 'sort-box-asc'" @click="toggleSort">
								<text>{{ sortOrder === 'desc' ? '新单在前' : '新单在后' }}</text>
							</view>
						</view>

						<!-- 批量操作区域 - 待派单页面 -->
						<view class="batch-operation-container" v-if="currentTab == 0">
							<view class="batch-select-area">
								<view class="select-all-checkbox" @click="toggleSelectAll">
									<view class="checkbox-icon" :class="{'checked': isAllSelected}">
										<text v-if="isAllSelected" class="check-mark">✓</text>
									</view>
									<text class="select-all-text">全选 ({{ selectedOrders.length }}/{{ getCurrentOrderList().length }})</text>
								</view>
							</view>
							<view class="batch-buttons-area" v-if="selectedOrders.length > 0">
								<view class="batch-btn batch-btn-primary" @click="batchDispatch">
									批量派单({{ selectedOrders.length }})
								</view>
							</view>
						</view>

						<view class="content2">
							<view>
								<view v-if="orderingList.length">
									<view class="article" v-for="(item, index) in orderingList" :key="index"
										:data-id="item.orderId" @click.stop="goOrderDetail">
										<!-- 订单头部信息 -->
										<view class="order-header">
											<view class="order-time">
												<!-- 订单选择复选框 - 待派单页面 -->
												<view class="order-checkbox-inline" v-if="currentTab == 0" @click.stop="toggleOrderSelection(item)">
													<view class="order-checkbox" :class="{'checked': item.isSelected}">
														<text v-if="item.isSelected" class="check-mark">✓</text>
													</view>
												</view>
												<image v-if="item.orderSourceImage" class="order-source-icon"
													:src="item.orderSourceImage"
													mode="scaleToFill" />
												<text v-if="item.mark" class="mark-text">{{ item.mark }}</text>
												<text class="time-text">{{ item.orderDate }}</text>
											</view>
											<view class="order-number">订单号：{{ item.orderNum }}</view>
										</view>
										<!-- 客户信息 -->
										<view class="customer-info">
											<view class="customer-main">
												<view class="customer-name-phone">
													<view v-if="item.isSend == 2" class="urge-badge">催单中</view>
													<text class="customer-name">{{ item.name }}</text>
													<view>
													<view class="customer-phone" @click.stop="copyText(item.phone)">{{ item.phone }}</view>
													<view class="copy-hint" @click.stop="copyText(item.phone)">点击复制</view></view>
												</view>
												<view class="action-buttons">
													<view @click.stop="refreshPhone(item.orderId)" class="action-btn refresh-btn">
														刷新电话
													</view>
													<view @click.stop="callPhone" :data-phone="item.phone" class="action-btn call-btn">
														打电话
													</view>
												</view>
											</view>
											<view class="customer-address" @click.stop="copyText(item.underway)">{{ item.underway }}</view>
											<text class="copy-hint address-copy-hint" @click.stop="copyText(item.underway)">点击复制地址</text>
										</view>
										<view class="artBottom font-size-28 padding-bottom-20">
											<!-- 商品信息简化显示 -->
											<view class="goods-info-simple">
												<view v-for="(it, imgIndex) in item.specList" :key="imgIndex" class="goods-item-simple">
													<text class="goods-name">{{ it.name || it.goodsName }}</text>
													<text class="goods-quantity">×{{ it.standardNumber }}</text>
												</view>
											</view>

											<!-- 订单金额信息 -->
											<view class="order-amount-info">
												<view class="amount-left">
													<view class="total-items">共{{ item.totalPieces }}件商品</view>
													<view v-if="item.transtatus != 3" class="amount-details">
														<view class="amount-row">
															<text class="amount-label">订单总额：</text>
															<text class="amount-value">￥{{ item.footing }}</text>
														</view>
														<view v-if="item.orderDiscounts != '0.00' && item.orderDiscounts" class="amount-row discount">
															<text class="amount-label">水票抵扣：</text>
															<text class="amount-value">-￥{{ item.orderDiscounts }}</text>
														</view>
														<view v-if="item.ticketPrice != '0.00' && item.ticketPrice" class="amount-row discount">
															<text class="amount-label">优惠券：</text>
															<text class="amount-value">-￥{{ item.ticketPrice }}</text>
														</view>
														<view v-if="item.yunfei != '0.00' && item.yunfei" class="amount-row">
															<text class="amount-label">配送费：</text>
															<text class="amount-value">+￥{{ item.yunfei }}</text>
														</view>
													</view>
													<view class="final-amount">
														<text class="final-label">实付金额：</text>
														<text class="final-value">￥{{ item.total }}</text>
													</view>
												</view>
												<view class="more-btn">
													<text>查看详情</text>
													<image :src="imgUri + '/images/jt-grey-right.png'" class="arrow-icon"></image>
												</view>
											</view>
											<view v-if="item.isYaTong"
												class="artTopLTxt padding-bottom-20 flex align-items-center"
												style='color:#777777;'>
												<view style="color: blue;font-weight: 600;">
													该订单已完成押桶
												</view>
											</view>
										</view>
										<!-- 操作按钮区域 -->
										<view class="action-buttons-area">
											<view v-if="item.transtatus == 1 || item.transtatus == 2">
												<view class="artTopRbtn font-size-28 bold actArtBtnBlue "
													:data-order-id="item.orderId"
													:data-store-order-id="item.storeOrderId"
													@click.stop="cancelExchangeOrder">
													撤销转单
												</view>
											</view>
											<view v-if="item.transtatus == 3">
												<view class="artTopRbtn font-size-28 bold actArtBtnBlue "
													:data-order-id="item.orderId"
													:data-store-order-id="item.storeOrderId"
													@click.stop="backExchangeOrder">
													回退转单
												</view>
											</view>
											<view v-else>

												<view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10"
													:data-id="item.orderId" data-orderStatus="2" data-state="0"
													@click.stop="updateOrderState" v-if="item.orderStatus == 1">
													接单
												</view>

												<view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10"
													:data-isalert="item.isAlert" :data-id="item.orderId"
													:data-userid="item.id" :data-ordernum="item.orderNum" data-type="0"
													@click.stop="choosePeopleAlert"
													v-if="item.orderStatus == 2 && item.state == 0 && !item.transtatus">
													派送
												</view>

												<!-- <view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10" :data-id="item.orderId" :data-userid="item.id" :data-ordernum="item.orderNum" data-type="0" @click.stop="setUpCommission">
														{{item.commission == 1 ? '设置提成' : '修改提成'}}
												</view> -->
												<view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10"
													:data-id="item.orderId" data-orderStatus="2" data-state="0"
													@click.stop="updateOrderState"
													v-if="item.orderStatus == 2 && item.state == 1 && !item.deliveryName">
													取消发单
												</view>
												<view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10"
													:data-id="item.orderId" data-orderStatus="3" data-state="1"
													@click.stop="updateOrderState"
													v-if="item.orderStatus == 2 && item.state == 2 && !item.deliveryId">
													发货
												</view>
												<view class="artTopRbtn font-size-28 bold  margin-right-10"
													@click.stop="updateOrderState"
													v-if="item.orderStatus == 3 && item.state == 2">
													已发货
												</view>

												<view class="artTopRbtn font-size-28 bold  margin-right-10"
													style="border:none;" @click.stop="updateOrderState"
													v-if="item.orderStatus == 3 && item.state == 3">
													已签收
												</view>
												<!-- <view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-30" :data-isalert="item.isAlert"
												 :data-id="item.orderId" :data-userid="item.id" :data-ordernum="item.orderNum" data-type="0" @click.stop="merchantDeliverSelf">
													自送
												</view> -->
												<view v-if="item.affirm == 0"
													class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10"
													:data-id="item.orderId" :data-userid="item.id"
													:data-ordernum="item.orderNum" data-type="0"
													@click.stop="confirmCollection">
													确认收款
												</view>

												<view v-else-if="item.affirm == 1"
													class="artTopRbtn font-size-28 bold actArtBtn888 margin-right-10">
													已收款</view>
												<view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10"
													:data-id="item.orderId" :data-userid="item.id"
													@click.stop="toExchangeOrder" v-if="!item.transtatus">
													转单
												</view>
												<view v-if="item.orderStatus == 2 && item.state == 0 && item.ordersource != null && item.ordersource != 0"
													class="artTopRbtn font-size-28 bold actArtBtnOrange margin-right-10"
													:data-id="item.orderId"
													@click.stop="openBackOrderModal">
													回退订单
												</view>
												<!-- 催单发货按钮 -->
												<view class="artTopRbtn font-size-28 bold actArtBtnPurple margin-right-10"
													:data-id="item.orderId"
													@click.stop="urgeStore">
													催单发货
												</view>
												<!-- 操作记录按钮 -->
												<view class="artTopRbtn font-size-28 bold actArtBtnGreen margin-right-10"
													:data-ordernum="item.orderNum"
													@click.stop="showOrderLog">
													操作记录
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="no" v-if="orderingList.length == '0'">
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">暂无需要处理的订单</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>
				<!-- 转单订单 -->
				<swiper-item class="position-relative">
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">

						<view >
							<view>
								<view v-if="orderingList.length">
									<view class="transfer-order-item" v-for="(item, index) in orderingList" :key="index"
										:data-id="item.orderId" @click.stop="goTranDetail">

										<!-- 转单订单头部信息 -->
										<view class="transfer-header-info">
											<view class="shop-name">
												<text class="label">转单店铺：</text>
												<text class="value">{{ item.transtatus == 3 ? item.oldStoreName : item.storeName }}</text>
											</view>
											<view class="shop-name" v-if="item.transtatus == 3">
												<text class="label">抢单店铺：</text>
												<text class="value">{{ item.storeName }}</text>
											</view>
											<view class="order-time">
												<text class="label">下单时间：</text>
												<text class="value">{{ item.orderDate }}</text>
											</view>
											<view class="order-time">
												<text class="label">订单状态：</text>
												<text class="value">{{ item.newOrderState }}</text>
											</view>
											<view class="order-time">
												<text class="label">转单状态：</text>
												<text class="value">{{ item.transtatus == null ? '未转单' : transferStatus[item.transtatus].name }}</text>
											</view>
										</view>

										<!-- 客户信息 -->
										<view class="transfer-user-info">
											<view class="user-details">
												<view class="user-name">
													<text>{{ item.userName }}</text>
													<text class="user-phone">{{ item.userPhone }}</text>
												</view>
												<view class="user-address">{{ item.userAddress }}</view>
											</view> 
										</view>

										<!-- 商品信息 -->
										<view class="transfer-goods" v-if="item.list && item.list.length > 0 && item.list[0].orderShopDeatilList && item.list[0].orderShopDeatilList.length > 0">
											<view class="goods-item" v-for="(detailItem, detailIndex) in item.list[0].orderShopDeatilList" :key="detailIndex">
												<view class="goods-image">
													<image class="imgItem" mode="aspectFit" :src="detailItem.image"></image>
												</view>
												<view class="goods-info">
													<view class="goods-title">{{ detailItem.title }}</view>
													<view class="goods-count">共{{ detailItem.shopNumber }}件</view>
												</view>
											</view>
										</view>

										<!-- 订单金额信息 -->
										<view class="transfer-order-summary">
											<view class="order-total">
												<text>共 {{ item.orderTotalNumber }} 件商品</text>
												<text class="order-price">订单金额：￥{{ item.orderTotalPrice || 0 }}元</text>
											</view>
											<!-- <view class="order-total">
												<text></text>
												<text class="order-price">佣金：￥{{ item.oldprice }}元</text>
											</view> -->
											<view class="distance-info" v-if="item.distance">
												<text>客户距您{{ item.distance }}千米</text>
											</view>
										</view>

										<!-- 操作按钮 -->
										<view class="transfer-action-buttons">
											<view class="transfer-btn transfer-btn-detail" @click.stop="goTranDetail" :data-id="item.orderId">
												查看详情
											</view>
											<view class="transfer-btn transfer-btn-cancel"
												v-if="(item.transtatus == 2 || item.transtatus == 1)"
												:data-order-id="item.orderId" @click.stop="cancelExchangeOrder">
												撤销
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="no" v-if="orderingList.length == '0'">
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">暂无转单订单</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>

				<!-- 送水员待接单 -->
				<swiper-item class="position-relative">
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">
						<!-- 搜索框和排序按钮 -->
						<view class="search-sort-container">
							<view class="search-box">
								<input type="text" v-model="searchName" placeholder="请输入关键词搜索" @input="handleSearch" />
								<view class="search-btn" @click="doSearch">
									<text>搜索</text>
								</view>
							</view>
							<view :class="sortOrder === 'desc' ? 'sort-box-desc' : 'sort-box-asc'" @click="toggleSort">
								<text>{{ sortOrder === 'desc' ? '新单在前' : '新单在后' }}</text>
							</view>
						</view>

						<!-- 批量操作区域 - 配送中页面 -->
						<view class="batch-operation-container" v-if="currentTab == 2">
							<view class="batch-select-area">
								<view class="select-all-checkbox" @click="toggleSelectAll">
									<view class="checkbox-icon" :class="{'checked': isAllSelected}">
										<text v-if="isAllSelected" class="check-mark">✓</text>
									</view>
									<text class="select-all-text">全选 ({{ selectedOrders.length }}/{{ getCurrentOrderList().length }})</text>
								</view>
							</view>
							<view class="batch-buttons-area" v-if="selectedOrders.length > 0">
								<view class="batch-btn batch-btn-primary" @click="batchDispatchChange">
									批量改派({{ selectedOrders.length }})
								</view>
							</view>
						</view>

						<view class="content2">
							<view>
								<view v-if="orderingList.length">
									<view class="article" v-for="(item, index) in orderingList" :key="index"
										:data-id="item.orderId" @click.stop="goOrderDetail">
										<!-- 订单头部信息 -->
										<view class="order-header">
											<view class="order-time">
												<!-- 订单选择复选框 - 配送中页面 -->
												<view class="order-checkbox-inline" v-if="currentTab == 2" @click.stop="toggleOrderSelection(item)">
													<view class="order-checkbox" :class="{'checked': item.isSelected}">
														<text v-if="item.isSelected" class="check-mark">✓</text>
													</view>
												</view>
												<image v-if="item.orderSourceImage" class="order-source-icon"
													:src="item.orderSourceImage"
													mode="scaleToFill" />
												<text v-if="item.mark" class="mark-text">{{ item.mark }}</text>
												<text class="time-text">{{ item.orderDate }}</text>
											</view>
											<view class="order-number">订单号：{{ item.orderNum }}</view>
										</view>
										<view class="artTop flex">
											<view >
												<view class="font-size-30 bold"
													style="margin-bottom:10rpx; display: flex;align-items: center;">
													<view v-if="item.isSend == 2" class="urge-icon margin-right-10">催单中
													</view>
													<text>{{ item.name }}</text>
													<view>
													<view class="margin-left-20" @click.stop="copyText(item.phone)">{{ item.phone }}</view>
													<view class="copy-hint" style="margin-left: 20rpx ;" @click.stop="copyText(item.phone)">点击复制</view></view>
													<view @click.stop="refreshPhone(item.orderId)"
														class="margin-left-20 bold"
														style="width: 140rpx;color: white;background: #e6a23c;display: flex;align-items: center;justify-content: center;height: 50rpx;border-radius: 50rpx;font-size: 28rpx;">
														刷新电话</view>
													<view @click.stop="callPhone" :data-phone="item.phone"
														class="margin-left-20 bold"
														style="width: 130rpx;color: white;background: #1794fd;display: flex;align-items: center;justify-content: center;height: 50rpx;border-radius: 50rpx;font-size: 28rpx;">
														打电话</view>
														
												</view>
												<view class="artTopLTxt font-size-30 margin-bottom-20"
													style="color:#666;" @click.stop="copyText(item.underway)">{{ item.underway }}</view>
												<text class="copy-hint address-copy-hint" @click.stop="copyText(item.underway)">点击复制地址</text>
											</view>
											<!-- <view class="artTopR overflow-hidden">
												<view class="artTopRbtn font-size-28 bold actArtBtnBlue "
													:data-id="item.orderId" data-orderStatus="2" data-state="0"
													@click.stop="updateOrderState" v-if="item.orderStatus == 1">
													接单
												</view>

												<view class="artTopRbtn font-size-28 bold actArtBtnBlue"
													:data-isalert="item.isAlert" :data-id="item.orderId"
													:data-userid="item.id" :data-ordernum="item.orderNum" data-type="0"
													@click.stop="choosePeopleAlert"
													v-if="item.orderStatus == 2 && item.state == 0">
													固定送水员
												</view>

												<view class="artTopRbtn font-size-28 bold actArtBtnBlue "
													:data-id="item.orderId" data-orderStatus="2" data-state="0"
													@click.stop="updateOrderState"
													v-if="item.orderStatus == 2 && item.state == 1 && !item.deliveryName">
													取消发单
												</view>
												<view class="artTopRbtn font-size-28 bold actArtBtnBlue "
													:data-id="item.orderId" data-orderStatus="3" data-state="1"
													@click.stop="updateOrderState"
													v-if="item.orderStatus == 2 && item.state == 2 && !item.deliveryId">
													发货
												</view>
												<view class="artTopRbtn font-size-28 bold  "
													@click.stop="updateOrderState"
													v-if="item.orderStatus == 3 && item.state == 2">
													已发货
												</view>
												<view class="artTopRbtn font-size-28 bold  "
													style="border:none;" @click.stop="updateOrderState"
													v-if="item.orderStatus == 3 && item.state == 3">
													已签收
												</view>
											</view> -->
										</view>
										<!-- 商品信息简化显示 -->
										<view class="goods-info-simple">
											<view v-for="(it, imgIndex) in item.specList" :key="imgIndex" class="goods-item-simple">
												<text class="goods-name">{{ it.name || it.goodsName }}</text>
												<text class="goods-quantity">×{{ it.standardNumber }}</text>
											</view>
										</view>

										<!-- 订单金额信息 -->
										<view class="order-amount-info">
											<view class="amount-left">
												<view class="total-items">共{{ item.totalPieces }}件商品</view>
												<view v-if="item.transtatus != 3" class="amount-details">
													<view class="amount-row">
														<text class="amount-label">订单总额：</text>
														<text class="amount-value">￥{{ item.footing }}</text>
													</view>
													<view v-if="item.orderDiscounts != '0.00' && item.orderDiscounts" class="amount-row discount">
														<text class="amount-label">水票抵扣：</text>
														<text class="amount-value">-￥{{ item.orderDiscounts }}</text>
													</view>
													<view v-if="item.ticketPrice != '0.00' && item.ticketPrice" class="amount-row discount">
														<text class="amount-label">优惠券：</text>
														<text class="amount-value">-￥{{ item.ticketPrice }}</text>
													</view>
													<view v-if="item.yunfei != '0.00' && item.yunfei" class="amount-row">
														<text class="amount-label">配送费：</text>
														<text class="amount-value">+￥{{ item.yunfei }}</text>
													</view>
												</view>
												<view class="final-amount">
													<text class="final-label">实付金额：</text>
													<text class="final-value">￥{{ item.total }}</text>
												</view>
											</view>
											<view class="more-btn">
												<text>查看详情</text>
												<image :src="imgUri + '/images/jt-grey-right.png'" class="arrow-icon"></image>
											</view>
										</view>
										<view
											class="padding-bottom-20 padding-top-20 font-size-28 bold border-top flex align-items-center justify-content-between"
											v-if="item.deliveryName && item.orderStatus != 3 && item.state != 3">
											<view>
												<view>
													接单人姓名 :
													<text class="color-blue margin-right-30">{{ item.deliveryName
													}}</text>
												</view>
												<view>
													接单人手机 :
													<text class="color-blue">{{ item.deliveryPhone }}</text>
												</view>
											</view>
										</view>
										<!-- <view class="padding-top-20 color-red font-size-28 border-top" v-if="item.orderStatus < 3">商品库存已锁定</view>
										<view v-else>
											<view class="padding-top-20 color-red font-size-28 border-top" v-if="item.orderStatus == 8 && item.returnState == 2">
												商品库存已归还
											</view>
											<view class="padding-top-20 color-red font-size-28 border-top" v-else>商品已出库</view>
										</view> -->
										<view class="overflow-hidden padding-top-20 border-top">
											<view v-if="item.affirm == 0"
												class="artTopRbtn font-size-28 bold actArtBtnBlue margin-left-10"
												:data-id="item.orderId" :data-userid="item.id"
												:data-ordernum="item.orderNum" data-type="0"
												@click.stop="confirmCollection">
												确认收款
											</view>
											<view v-else-if="item.affirm == 1"
												class="artTopRbtn font-size-28 bold actArtBtn888 margin-left-10">已收款
											</view>
											<view :data-order="item.orderNum" :data-orderId="item.orderId"
													:data-id="item.deliveryId" @click.stop="cancelDriveryOrder"
												class="artTopRbtn font-size-28 bold actArtBtn888 margin-left-10">撤销派单
											</view>
											<!-- 催单发货按钮 -->
											<view class="artTopRbtn font-size-28 bold actArtBtnPurple margin-left-10"
												:data-id="item.orderId"
												@click.stop="urgeStore">
												催单发货
											</view>

											<!-- <view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10" :data-id="item.orderId" :data-userid="item.id" :data-ordernum="item.orderNum" data-type="0" @click.stop="setUpCommission">
													{{item.commission == 1 ? '设置提成' : '修改提成'}}
											</view> -->
											<!-- 操作记录按钮 -->
											<view class="artTopRbtn font-size-28 bold actArtBtnGreen margin-left-10"
												:data-ordernum="item.orderNum"
												@click.stop="showOrderLog">
												操作记录
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="no" v-if="orderingList.length == '0'">
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">暂无需要处理的订单</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>

				<!-- 自送订单 -->
				<swiper-item class="position-relative">
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">
						<!-- 搜索框和排序按钮 -->
						<view class="search-sort-container">
							<view class="search-box">
								<input type="text" v-model="searchName" placeholder="请输入关键词搜索" @input="handleSearch" />
								<view class="search-btn" @click="doSearch">
									<text>搜索</text>
								</view>
							</view>
							<view :class="sortOrder === 'desc' ? 'sort-box-desc' : 'sort-box-asc'" @click="toggleSort">
								<text>{{ sortOrder === 'desc' ? '新单在前' : '新单在后' }}</text>
							</view>
						</view>

						<!-- 批量操作区域 - 自送订单页面 -->
						<view class="batch-operation-container" v-if="currentTab == 3">
							<view class="batch-select-area">
								<view class="select-all-checkbox" @click="toggleSelectAll">
									<view class="checkbox-icon" :class="{'checked': isAllSelected}">
										<text v-if="isAllSelected" class="check-mark">✓</text>
									</view>
									<text class="select-all-text">全选 ({{ selectedOrders.length }}/{{ getCurrentOrderList().length }})</text>
								</view>
							</view>
							<!-- <view class="batch-buttons-area" v-if="selectedOrders.length > 0">
								<view class="batch-btn batch-btn-success" @click="batchDelivered">
									批量送达({{ selectedOrders.length }})
								</view>
							</view> -->
						</view>

						<view class="content2">
							<view>
								<view v-if="myDispatchList.length">
									<view class="article" v-for="(item, index) in myDispatchList" :key="index"
										:data-id="item.orderId" @click.stop="goOrderDetail">
										<!-- 订单头部信息 -->
										<view class="order-header">
											<view class="order-time">
												<!-- 订单选择复选框 - 自送订单页面 -->
												<view class="order-checkbox-inline" v-if="currentTab == 3" @click.stop="toggleOrderSelection(item)">
													<view class="order-checkbox" :class="{'checked': item.isSelected}">
														<text v-if="item.isSelected" class="check-mark">✓</text>
													</view>
												</view>
												<image v-if="item.orderSourceImage" class="order-source-icon"
													:src="item.orderSourceImage"
													mode="scaleToFill" />
												<text v-if="item.mark" class="mark-text">{{ item.mark }}</text>
												<text class="time-text">{{ item.orderDate }}</text>
											</view>
											<view class="order-number">订单号：{{ item.orderNum }}</view>
											<view class="delivery-status-badges">
												<text v-if="item.pledgetCount" class="badge-pledge">押{{ item.pledgetCount }}</text>
												<text v-if="item.debtCount" class="badge-debt">欠{{ item.debtCount }}</text>
											</view>
										</view>
										<!-- 客户信息 -->
										<view class="customer-info">
											<view class="customer-main">
												<view class="customer-name-phone">
													<view class="customer-name">{{ item.name }}</view>
													<view>
													<view class="customer-phone" @click.stop="copyText(item.phone)">{{ item.phone }}</view>
													<view class="copy-hint" @click.stop="copyText(item.phone)">点击复制</view></view>
												</view>
												<view class="action-buttons">
													<view @click.stop="refreshPhone(item.orderId)" class="action-btn refresh-btn">
														刷新电话
													</view>
													<view @click.stop="callPhone" :data-phone="item.phone" class="action-btn call-btn">
														打电话
													</view>
												</view>
											</view>
											<view class="customer-address" @click="copyText(item.underway)">{{ item.underway }}</view>
											<text class="copy-hint address-copy-hint">点击复制地址</text>
										</view>
										<!-- 配送状态显示 -->
										<view class="delivery-status">
											<view class="status-badge status-delivering" v-if="item.orderStatus == 4 && item.deliveryInfoState == 0">
												配送中
											</view>
											<view class="status-badge status-delivered" v-if="item.orderStatus == 4 && item.deliveryInfoState != 0">
												已送达
											</view>
											<view class="status-badge status-signed" v-if="item.orderStatus == 5">
												已签收
											</view>
											<view class="status-badge status-completed" v-if="item.orderStatus == 10">
												已完成
											</view>
										</view>
										<!-- 商品信息简化显示 -->
										<view class="goods-info-simple">
											<view v-for="(it, imgIndex) in item.specList" :key="imgIndex" class="goods-item-simple">
												<text class="goods-name">{{ it.name || it.goodsName }}</text>
												<text class="goods-quantity">×{{ it.standardNumber }}</text>
											</view>
										</view>

										<!-- 订单金额信息 -->
										<view class="order-amount-info">
											<view class="amount-left">
												<view class="total-items">共{{ item.totalPieces }}件商品</view>
												<view v-if="item.transtatus != 3" class="amount-details">
													<view class="amount-row">
														<text class="amount-label">订单总额：</text>
														<text class="amount-value">￥{{ item.footing }}</text>
													</view>
													<view v-if="item.orderDiscounts != '0.00' && item.orderDiscounts" class="amount-row discount">
														<text class="amount-label">水票抵扣：</text>
														<text class="amount-value">-￥{{ item.orderDiscounts }}</text>
													</view>
													<view v-if="item.ticketPrice != '0.00' && item.ticketPrice" class="amount-row discount">
														<text class="amount-label">优惠券：</text>
														<text class="amount-value">-￥{{ item.ticketPrice }}</text>
													</view>
													<view v-if="item.yunfei != '0.00' && item.yunfei" class="amount-row">
														<text class="amount-label">配送费：</text>
														<text class="amount-value">+￥{{ item.yunfei }}</text>
													</view>
												</view>
												<view class="final-amount">
													<text class="final-label">实付金额：</text>
													<text class="final-value">￥{{ item.total }}</text>
												</view>
											</view>
											<view class="more-btn">
												<text>查看详情</text>
												<image :src="imgUri + '/images/jt-grey-right.png'" class="arrow-icon"></image>
											</view>
										</view>
										<view v-if="item.isYaTong" class="ya-tong-notice">
											<text class="ya-tong-text">该订单已完成押桶</text>
										</view>
										<!-- 操作按钮区域 -->
										<view class="action-buttons-area">
											<!-- <view @click="uploadpic(item.orderId)" class="artTopRbtn actArtBtnBlue">
												拍照
											</view> -->
											<view v-if="item.userBuck == 1"
												class="artTopRbtn actArtBtnBlue"
												:data-isalert="item.isAlert"
												:data-id="item.orderId"
												:data-userid="item.id"
												:data-ordernum="item.orderNum"
												:data-index="index"
												data-type="0"
												@click.stop="goBackBucket">
												回桶
											</view>
											<view v-if="item.userBuck == 1"
												class="artTopRbtn actArtBtnRed"
												:data-isalert="item.isAlert"
												:data-id="item.orderId"
												:data-userid="item.id"
												:data-ordernum="item.id"
												data-type="0"
												@click.stop="noBackBucket">
												不回桶
											</view>
											<view v-if="item.deliveryUserBuck == 0"
												class="artTopRbtn actArtBtnBlue"
												:data-isalert="item.isAlert"
												:data-id="item.orderId"
												:data-userid="item.id"
												:data-ordernum="item.orderNum"
												:data-index="index"
												data-type="0"
												@click.stop="goKeHuBucket">
												客户押桶
											</view>
											<view v-if="(item.orderStatus == 4 || item.orderStatus == 3 || item.orderStatus == 2) && item.deliveryInfoState == 0"
												class="artTopRbtn actArtBtnBlue"
												:data-id="item.orderId"
												:data-userid="item.id"
												:data-ordernum="item.orderNum"
												data-type="0"
												@click.stop="merchantDeliverSelfComplete">
												确认送达
											</view>
											<view v-if="(item.orderStatus == 4 || item.orderStatus == 3 || item.orderStatus == 2) && item.deliveryInfoState == 0"
												class="artTopRbtn actArtBtnOrange"
												:data-id="item.orderId"
												:data-userid="item.id"
												:data-ordernum="item.orderNum"
												data-type="0"
												@click.stop="backOrder">
												回退订单
											</view>
											<view v-if="item.affirm == 0"
												class="artTopRbtn actArtBtnBlue"
												:data-id="item.orderId"
												:data-userid="item.id"
												:data-ordernum="item.orderNum"
												data-type="0"
												@click.stop="confirmCollection">
												确认收款
											</view>
											<view v-else-if="item.affirm == 1"
												class="artTopRbtn actArtBtnGray">
												已收款
											</view>
											<!-- 催单发货按钮 -->
											<view class="artTopRbtn actArtBtnPurple"
												:data-id="item.orderId"
												@click.stop="urgeStore">
												催单发货
											</view>
											<!-- 操作记录按钮 -->
											<view class="artTopRbtn actArtBtnGreen"
												:data-ordernum="item.orderNum"
												@click.stop="showOrderLog">
												操作记录
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="no" v-if="myDispatchList.length == '0'">
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">暂无自送订单</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>


				<!-- 待退货 -->
				<swiper-item class="position-relative">
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">
						<!-- 搜索框和排序按钮 -->
						<view class="search-sort-container">
							<view class="search-box">
								<input type="text" v-model="searchName" placeholder="请输入关键词搜索" @input="handleSearch" />
								<view class="search-btn" @click="doSearch">
									<text>搜索</text>
								</view>
							</view>
							<view :class="sortOrder === 'desc' ? 'sort-box-desc' : 'sort-box-asc'" @click="toggleSort">
								<text>{{ sortOrder === 'desc' ? '新单在前' : '新单在后' }}</text>
							</view>
						</view>
						<view class="content2">
							<view class="article" v-for="(item, index) in afterSaleList" :key="index"
								@click.stop="goDetail" :data-id="item.retreatId">
								<!-- 退货申请头部信息 -->
								<view class="return-header">
									<view class="return-order-info">
										<text class="return-order-number">订单编号: {{ item.orderId }}</text>
										<view class="return-type-badge">
											<text class="return-type-text">{{ item.returnName }}申请</text>
										</view>
									</view>
								</view>

								<!-- 客户信息 -->
								<view class="return-customer-info">
									<view class="customer-detail">
										<view class="customer-row">
											<text class="customer-label">联系人：</text>
											<text class="customer-value">{{ item.username }}</text>
										</view>
										<view class="customer-row">
											<text class="customer-label">联系方式：</text>
											<text class="customer-value" @click="copyText(item.mobile)">{{ item.mobile }}</text>
											<text class="copy-hint">点击复制</text>
										</view>
										<view class="customer-row">
											<text class="customer-label">地址：</text>
											<text class="customer-value" @click="copyText(item.address)">{{ item.address }}</text>
											<text class="copy-hint">点击复制</text>
										</view>
									</view>
								</view>

								<!-- 商品信息简化显示 -->
								<view class="return-goods-info">
									<view v-for="(it, idx) in item.specList" :key="idx" class="return-goods-item">
										<text class="return-goods-name">{{ it.goodsName }}</text>
										<view class="return-goods-details">
											<text class="return-goods-price">￥{{ it.price }}</text>
											<text class="return-goods-quantity">×{{ it.standardNumber }}</text>
										</view>
									</view>
								</view>

								<!-- 退货金额和操作按钮 -->
								<view class="return-action-area">
									<view class="return-amount-info">
										<text class="return-amount-label">共{{ item.number }}件</text>
										<text class="return-amount-value">金额：￥{{ item.money }}</text>
									</view>
									<view class="return-action-buttons">
										<view v-if="item.status == '0'" class="return-buttons-pending">
											<view class="return-btn return-btn-reject"
												@click.stop="dealSale"
												:data-state="2"
												:data-id="item.retreatId"
												:data-index="index">
												拒绝
											</view>
											<view class="return-btn return-btn-approve"
												@click.stop="dealSale"
												:data-state="1"
												:data-id="item.retreatId"
												:data-index="index">
												同意
											</view>
										</view>
										<view v-if="item.status == '1'" class="return-status-approved">已同意</view>
										<view v-if="item.status == '2'" class="return-status-rejected">已拒绝</view>
									</view>
								</view>
							</view>
							<view class="no" v-if="afterSaleList.length == '0'">
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">暂无退货申请</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>

				<!-- 已完成 -->
				<swiper-item class="position-relative">
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">
						<!-- 搜索框和排序按钮 -->
						<view class="search-sort-container">
							<view class="search-box">
								<input type="text" v-model="searchName" placeholder="请输入关键词搜索" @input="handleSearch" />
								<view class="search-btn" @click="doSearch">
									<text>搜索</text>
								</view>
							</view>
							<view :class="sortOrder === 'desc' ? 'sort-box-desc' : 'sort-box-asc'" @click="toggleSort">
								<text>{{ sortOrder === 'desc' ? '新单在前' : '新单在后' }}</text>
							</view>
						</view>
						<view class="content2">
							<view v-if="is_result1">
								<view class="completed-summary" v-if="orverOrderList.length">
									<view class="summary-item">
										<text class="summary-label">已完成订单总数：</text>
										<text class="summary-value">{{ orverOrderListCount }}单</text>
									</view>
								</view>
								<view class="article" v-for="(item, index) in orverOrderList" :key="index"
									:data-id="item.orderId" @click.stop="goOrderDetail">
									<!-- 订单头部信息 -->
									<view class="order-header">
										<view class="order-time">
											<image v-if="item.orderSourceImage" class="order-source-icon"
												:src="item.orderSourceImage"
												mode="scaleToFill" />
											<text v-if="item.mark" class="mark-text">{{ item.mark }}</text>
											<text class="time-text">完成时间：{{ item.time }}</text>
										</view>
										<view class="order-number">订单号：{{ item.orderNum }}</view>
										<view class="completed-actions" v-if="item.applyBuck == 1">
											<view @click.stop="updateDeliveryInfo" :data-ordernum="item.orderNum" class="action-btn-small cancel-btn">
												撤销押桶
											</view>
											<view @click.stop="updateDeliveryInfo" :data-ordernum="item.orderNum" class="action-btn-small confirm-btn">
												确认押桶
											</view>
										</view>
									</view>
									<!-- 客户信息 -->
									<view class="customer-info">
										<view class="customer-main">
											<view class="customer-name-phone">
												<text class="customer-name">{{ item.name }}</text>
												<view>
												<view class="customer-phone" @click="copyText(item.phone)">{{ item.phone }}</view>
												<view class="copy-hint">点击复制</view></view>
											</view>
											<view v-if="item.applyBuck == 1" class="bucket-status-badge">
												送水员已发起押桶
											</view>
										</view>
										<view class="customer-address" @click="copyText(item.underway)">{{ item.underway }}</view>
										<text class="copy-hint address-copy-hint">点击复制地址</text>
									</view>
									<!-- 商品信息简化显示 -->
									<view class="goods-info-simple">
										<view v-for="(it, imgIndex) in item.specList" :key="imgIndex" class="goods-item-simple">
											<text class="goods-name">{{ it.name || it.goodsName }}</text>
											<text class="goods-quantity">×{{ it.standardNumber }}</text>
										</view>
									</view>

									<!-- 订单金额信息 -->
									<view class="order-amount-info">
										<view class="amount-left">
											<view class="total-items">共{{ item.totalPieces }}件商品</view>
											<view v-if="item.transtatus != 3" class="amount-details">
												<view class="amount-row">
													<text class="amount-label">订单总额：</text>
													<text class="amount-value">￥{{ item.footing }}</text>
												</view>
												<view v-if="item.orderDiscounts != '0.00' && item.orderDiscounts" class="amount-row discount">
													<text class="amount-label">水票抵扣：</text>
													<text class="amount-value">-￥{{ item.orderDiscounts }}</text>
												</view>
												<view v-if="item.ticketPrice != '0.00' && item.ticketPrice" class="amount-row discount">
													<text class="amount-label">优惠券：</text>
													<text class="amount-value">-￥{{ item.ticketPrice }}</text>
												</view>
												<view v-if="item.yunfei != '0.00' && item.yunfei" class="amount-row">
													<text class="amount-label">配送费：</text>
													<text class="amount-value">+￥{{ item.yunfei }}</text>
												</view>
											</view>
											<view class="final-amount">
												<text class="final-label">实付金额：</text>
												<text class="final-value">￥{{ item.total }}</text>
											</view>
										</view>
										<view class="more-btn">
											<text>查看详情</text>
											<image :src="imgUri + '/images/jt-grey-right.png'" class="arrow-icon"></image>
										</view>
									</view>
									<!-- 配送员信息 -->
									<view class="delivery-info" v-if="item.deliveryName">
										<view class="delivery-person">
											<view class="delivery-detail">
												<text class="delivery-label">接单人姓名：</text>
												<text class="delivery-value">{{ item.deliveryName }}</text>
											</view>
											<view class="delivery-detail">
												<text class="delivery-label">接单人手机：</text>
												<text class="delivery-value">{{ item.deliveryPhone }}</text>
											</view>
										</view>
									</view>

									<!-- 操作按钮区域 -->
									<view class="action-buttons-area">
										<view v-if="item.affirm == 0"
											class="artTopRbtn actArtBtnBlue"
											:data-id="item.orderId"
											:data-userid="item.id"
											:data-ordernum="item.orderNum"
											data-type="0"
											@click.stop="confirmCollection">
											确认收款
										</view>
										<view v-else-if="item.affirm == 1"
											class="artTopRbtn actArtBtnGray">
											已收款
										</view>
									</view>
								</view>
							</view>
							<view class="no" v-else>
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">暂无已完成的订单</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>

				<!-- 送水员已发起押桶 -->
				<swiper-item class="position-relative">
					

				<!-- 门店收款码悬浮窗 -->
				<view v-if="store && store.shoukuanma" class="payment-code-float" @click="showPaymentCode">
					 <!-- <image src="https://waterstation.com.cn/szm/upload/qrcode-icon.png" mode="aspectFit"></image> -->
					<text>收款码</text>
				</view>
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">
						<view class="content2 ">
							<view v-if="yatongList && yatongList.length > 0">
								<view>
									<view class="navBox" v-if="yatongSum">
										<view class="font-size-32 color-red">
											押桶申请总数：
											<text class="bold margin-left-20">{{ yatongSum }}单</text>
										</view>
									</view>
									<view class="article" v-for="(item, index) in yatongList" :key="index"
										:data-id="item.orderId" @click.stop="goOrderDetail">
										<view class="font-size-24 margin-bottom-10 flex justify-content-between">
											<text>下单时间：{{ item.orderDate }}</text>
											<view @click.stop="updateDeliveryInfo" :data-ordernum="item.orderNum"
												class="go-back-list">撤销押桶</view>
											<view @click.stop="confirmPledgeBuck(item.orderId,item.id)"
												class="go-back-list-confirm">确认押桶</view>
											<view class="go-back-list-log"
												:data-ordernum="item.orderNum"
												@click.stop="showOrderLog">操作记录</view>
										</view>
										<view class="artTop flex">
											<view class="artTopL-short">
												<view class="font-size-30 bold" style="margin-bottom:10rpx;">
													{{ item.name }}
													<view class="margin-left-20" :data-phone="item.phone"
														@click.stop="callPhone">{{ item.phone }}</view>
													<view class="copy-hint" @click.stop="copyText(item.phone)">点击复制</view>
												</view>
												<view class="artTopLTxt font-size-30 margin-bottom-20"
													style="color:#666;" @click.stop="copyText(item.underway)">{{ item.underway }}</view>
												<text class="copy-hint address-copy-hint" @click.stop="copyText(item.underway)">点击复制地址</text>
											</view>
											<view
												class="artTopR-short overflow-hidden font-size-30 color-green text-align-center">
												送水员已发起押桶</view>
										</view>
										<view class="artBottom font-size-28 padding-bottom-20">
											<!-- 商品信息简化显示 -->
											<view class="goods-info-simple">
												<view v-for="(it, imgIndex) in item.specList" :key="imgIndex" class="goods-item-simple">
													<text class="goods-name">{{ it.name || it.goodsName }}</text>
													<text class="goods-quantity">×{{ it.standardNumber }}</text>
												</view>
											</view>

											<view class="flex align-items-center justify-content-between">
												<view v-if="item.transtatus != 3">
													<view>
														<text>共{{ item.totalPieces }}件商品</text>
														<text class="margin-left-30">订单总金额：￥{{ item.footing }}元</text>
													</view>
													<view v-if="item.orderDiscounts != '0.00' && item.orderDiscounts">
														<text>水票抵扣金额：￥{{ item.orderDiscounts }}元</text>
													</view>
													<view v-if="item.ticketPrice != '0.00' && item.ticketPrice">
														<text>优惠券：￥{{ item.ticketPrice }}元</text>
													</view>
													<view v-if="item.yunfei != '0.00' && item.yunfei">
														<text>配送费：￥{{ item.yunfei }}元</text>
													</view>
													<view>
														<text>订单实付金额：￥{{ item.total }}元</text>
													</view>
												</view>
												<view v-else>
													<view>
														<text>订单实付金额：￥{{ item.total }}元</text>
													</view>
												</view>
												<view class="more">
													查看详情
													<image :src="imgUri + '/images/jt-grey-right.png'"
														style="width:15rpx;height:20rpx; margin-left:5rpx;"></image>
												</view>
											</view>
											<!-- <view wx:if="{{item.orderDiscounts != 0 && item.orderDiscounts}}">水票优惠 {{item.orderDiscounts}}元</view>
					  <text class='font-size-30 margin-right-20 bold'>订单总额：<text>￥</text>{{item.total}}</text>
					  <text style="color:#666;">共计{{item.totalPieces}}件商品</text> -->
										</view>
										<view
											class="padding-bottom-20 padding-top-20 font-size-28 bold border-top flex align-items-center justify-content-between"
											v-if="item.deliveryName">
											<view>
												<view>
													接单人姓名 :
													<text class="color-blue margin-right-30">{{ item.deliveryName
													}}</text>
												</view>
												<view>
													接单人手机 :
													<text class="color-blue" :data-phone="item.deliveryPhone"
														@click.stop="callPhone">{{ item.deliveryPhone }}</text>
												</view>
											</view>
											<view v-if="item.orderStatus == 2 && item.state == 2">
												<view class :data-order="item.orderId" :data-id="item.deliveryId"
													@click.stop="fixedDeliveryOrder"
													style="text-align:center;border:1px solid #09bb07;padding:5rpx 20rpx; color:#09bb07;border-radius:50rpx;margin-bottom:20rpx;">
													代接单
												</view>
												<view class :data-order="item.orderNum" :data-orderId="item.orderId"
													:data-id="item.deliveryId" @click.stop="cancelDriveryOrder"
													style="text-align:center;border:1px solid #1794fd;padding:5rpx 20rpx; color:#1794fd;border-radius:50rpx;margin-bottom:20rpx;">
													撤销派单
												</view>
											</view>
										</view>
										<!-- 				<view class="padding-top-20 color-red font-size-28 border-top" v-if="item.orderStatus < 3">商品库存已锁定</view>
										<view class="padding-top-20 color-red font-size-28 border-top" v-if="item.orderStatus >= 3 && item.orderStatus != 8">商品已出库</view>
										<view class="padding-top-20 color-red font-size-28 border-top" v-if="item.orderStatus == 8 && item.returnState == 2">商品库存已释放</view> -->
										<view class="overflow-hidden padding-top-20 border-top">

											<view v-if="item.affirm == 0"
												class="artTopRbtn font-size-28 bold actArtBtnBlue margin-left-10"
												:data-id="item.orderId" :data-userid="item.id"
												:data-ordernum="item.orderNum" data-type="0"
												@click.stop="confirmCollection">
												确认收款
											</view>
											<view v-else-if="item.affirm == 1"
												class="artTopRbtn font-size-28 bold actArtBtn888 margin-left-10">已收款
											</view>


											<!-- <view class="artTopRbtn font-size-28 bold actArtBtnBlue margin-right-10" :data-id="item.orderId" :data-userid="item.id" :data-ordernum="item.orderNum" data-type="0" @click.stop="setUpCommission">
													{{item.commission == 1 ? '设置提成' : '修改提成'}}
											</view> -->
										</view>
									</view>
								</view>
							</view>
							<!-- wx:if="{{!is_result}}" -->
							<view class="no" v-if="yatongList && yatongList.length == '0'">
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">暂无押桶申请</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>

				<!-- 今日订单 -->
				<swiper-item class="position-relative">
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">
						<!-- 搜索框和排序按钮 -->
						<view class="search-sort-container">
							<view class="search-box">
								<input type="text" v-model="searchName" placeholder="请输入关键词搜索" @input="handleSearch" />
								<view class="search-btn" @click="doSearch">
									<text>搜索</text>
								</view>
							</view>
							<view :class="sortOrder === 'desc' ? 'sort-box-desc' : 'sort-box-asc'" @click="toggleSort">
								<text>{{ sortOrder === 'desc' ? '新单在前' : '新单在后' }}</text>
							</view>
						</view>
						<view class="content2">
							<!-- 今日订单统计 -->
							<view class="today-summary">
								<view class="summary-card">
									<view class="summary-item">
										<text class="summary-label">今日订单总数</text>
										<text class="summary-value">{{ todaySum }}单</text>
									</view>
								</view>
								<view class="summary-card">
									<view class="summary-item">
										<text class="summary-label">今日实付总额</text>
										<text class="summary-value">￥{{ todayMoney }}</text>
									</view>
								</view>
							</view>
							<view v-if="todayOrderList && todayOrderList.length > 0">
								<view>
									<view class="article" v-for="(item, index) in todayOrderList" :key="index"
										:data-id="item.orderId" @click.stop="goOrderDetail">
										<!-- 订单头部信息 -->
										<view class="order-header">
											<view class="order-time">
												<text class="time-text">下单时间：{{ item.orderDate }}</text>
											</view>
											<view class="today-actions" v-if="item.applyBuck == 1">
												<view @click.stop="updateDeliveryInfo" :data-ordernum="item.orderNum" class="action-btn-small cancel-btn">
													撤销押桶
												</view>
											</view>
										</view>
										<!-- 客户信息和状态 -->
										<view class="customer-info">
											<view class="customer-main">
												<view class="customer-name-phone">
													<view v-if="item.isSend == 2" class="urge-badge">催单中</view>
													<text class="customer-name">{{ item.name }}</text>
													<text class="customer-phone">{{ item.phone }}</text>
												</view>
												<view class="action-buttons">
													<view @click.stop="refreshPhone(item.orderId)" class="action-btn refresh-btn">
														刷新电话
													</view>
													<view @click.stop="callPhone" :data-phone="item.phone" class="action-btn call-btn">
														打电话
													</view>
												</view>
											</view>
											<view class="customer-address">{{ item.underway }}</view>
											<view v-if="item.applyBuck == 1" class="bucket-status-badge">
												送水员已发起押桶
											</view>
										</view>
										<!-- 订单状态和操作按钮 -->
										<view class="order-status-actions">
											<view class="status-buttons">
												<view v-if="item.orderStatus == 1"
													class="status-btn status-btn-primary"
													:data-id="item.orderId"
													data-orderStatus="2"
													data-state="0"
													@click.stop="updateOrderState">
													接单
												</view>
												<view v-if="item.orderStatus == 2 && item.state == 0"
													class="status-btn status-btn-primary"
													:data-isalert="item.isAlert"
													:data-id="item.orderId"
													:data-userid="item.id"
													:data-ordernum="item.orderNum"
													data-type="0"
													@click.stop="choosePeopleAlert">
													固定送水员
												</view>
												<view v-if="item.orderStatus == 2 && item.state == 1"
													class="status-btn status-btn-warning"
													:data-id="item.orderId"
													data-orderStatus="2"
													data-state="0"
													data-cancel="1"
													@click.stop="updateOrderState">
													取消发单
												</view>
												<view v-if="item.orderStatus == 2 && item.state == 2 && !item.deliveryId"
													class="status-btn status-btn-primary"
													:data-id="item.orderId"
													data-orderStatus="3"
													data-state="1"
													@click.stop="updateOrderState">
													发货
												</view>
												<view v-if="item.orderStatus == 3 && item.state != 3"
													class="status-badge status-shipped">
													已发货
												</view>
												<view v-if="item.orderStatus == 3 && item.state == 3"
													class="status-badge status-signed">
													已签收
												</view>
												<view v-if="item.orderStatus == 10"
													class="status-badge status-completed">
													已完成
												</view>
												<view v-if="item.orderStatus == 9"
													class="status-badge status-reviewed">
													已评价
												</view>
												<view v-if="item.orderStatus == 6"
													class="status-badge status-cancelled">
													已取消
												</view>
												<view v-if="item.orderStatus == 7"
													class="status-badge status-rejected">
													已拒单
												</view>
												<view v-if="item.orderStatus == 8 && item.returnState == 1"
													class="status-badge status-return-pending">
													用户申请{{ item.returnName }}
												</view>
												<view v-if="item.orderStatus == 8 && item.returnState == 2"
													class="status-badge status-return-approved">
													已同意{{ item.returnName }}
												</view>
												<view v-if="item.orderStatus == 8 && item.returnState == 3"
													class="status-badge status-return-rejected">
													已拒绝{{ item.returnName }}
												</view>
											</view>
										</view>
										<!-- 商品信息简化显示 -->
										<view class="goods-info-simple">
											<view v-for="(it, imgIndex) in item.specList" :key="imgIndex" class="goods-item-simple">
												<text class="goods-name">{{ it.name || it.goodsName }}</text>
												<text class="goods-quantity">×{{ it.standardNumber }}</text>
											</view>
										</view>

										<!-- 订单金额信息 -->
										<view class="order-amount-info">
											<view class="amount-left">
												<view class="total-items">共{{ item.totalPieces }}件商品</view>
												<view v-if="item.transtatus != 3" class="amount-details">
													<view class="amount-row">
														<text class="amount-label">订单总额：</text>
														<text class="amount-value">￥{{ item.footing }}</text>
													</view>
													<view v-if="item.orderDiscounts != '0.00' && item.orderDiscounts" class="amount-row discount">
														<text class="amount-label">水票抵扣：</text>
														<text class="amount-value">-￥{{ item.orderDiscounts }}</text>
													</view>
													<view v-if="item.ticketPrice != '0.00' && item.ticketPrice" class="amount-row discount">
														<text class="amount-label">优惠券：</text>
														<text class="amount-value">-￥{{ item.ticketPrice }}</text>
													</view>
													<view v-if="item.yunfei != '0.00' && item.yunfei" class="amount-row">
														<text class="amount-label">配送费：</text>
														<text class="amount-value">+￥{{ item.yunfei }}</text>
													</view>
												</view>
												<view class="final-amount">
													<text class="final-label">实付金额：</text>
													<text class="final-value">￥{{ item.total }}</text>
												</view>
											</view>
											<view class="more-btn">
												<text>查看详情</text>
												<image :src="imgUri + '/images/jt-grey-right.png'" class="arrow-icon"></image>
											</view>
										</view>
										<!-- 配送员信息 -->
										<view class="delivery-info" v-if="item.deliveryName">
											<view class="delivery-person">
												<view class="delivery-detail">
													<text class="delivery-label">接单人姓名：</text>
													<text class="delivery-value">{{ item.deliveryName }}</text>
												</view>
												<view class="delivery-detail">
													<text class="delivery-label">接单人手机：</text>
													<text class="delivery-value">{{ item.deliveryPhone }}</text>
												</view>
											</view>
											<view v-if="item.orderStatus == 2 && item.state == 2" class="delivery-actions">
												<view class="action-btn-small confirm-btn"
													:data-order="item.orderId"
													:data-id="item.deliveryId"
													@click.stop="fixedDeliveryOrder">
													代接单
												</view>
												<view class="action-btn-small cancel-btn"
													:data-order="item.orderNum"
													:data-orderId="item.orderId"
													:data-id="item.deliveryId"
													@click.stop="cancelDriveryOrder">
													撤销派单
												</view>
											</view>
										</view>

										<!-- 操作按钮区域 -->
										<view class="action-buttons-area">
											<view v-if="item.affirm == 0"
												class="artTopRbtn actArtBtnBlue"
												:data-id="item.orderId"
												:data-userid="item.id"
												:data-ordernum="item.orderNum"
												data-type="0"
												@click.stop="confirmCollection">
												确认收款
											</view>
											<view v-else-if="item.affirm == 1"
												class="artTopRbtn actArtBtnGray">
												已收款
											</view>
											<!-- 催单发货按钮 -->
											<view class="artTopRbtn actArtBtnPurple"
												:data-id="item.orderId"
												@click.stop="urgeStore">
												催单发货
											</view>
											<!-- 操作记录按钮 -->
											<view class="artTopRbtn actArtBtnGreen"
												:data-ordernum="item.orderNum"
												@click.stop="showOrderLog">
												操作记录
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="no" v-if="todayOrderList && todayOrderList.length == '0'">
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">今日暂无订单</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>

				<!-- 全部订单 -->
				<swiper-item class="position-relative">
					<scroll-view scroll-y="true" style="height:100%;" @scrolltolower="lower"
						refresher-enabled="true"
						:refresher-triggered="refresherTriggered"
						@refresherrefresh="onRefresh"
						@refresherrestore="onRestore">
						<!-- 搜索框和排序按钮 -->
						<view class="search-sort-container">
							<view class="search-box">
								<input type="text" v-model="searchName" placeholder="请输入关键词搜索" @input="handleSearch" />
								<view class="search-btn" @click="doSearch">
									<text>搜索</text>
								</view>
							</view>
							<view :class="sortOrder === 'desc' ? 'sort-box-desc' : 'sort-box-asc'" @click="toggleSort">
								<text>{{ sortOrder === 'desc' ? '新单在前' : '新单在后' }}</text>
							</view>
						</view>
						<view class="content2">
							<view v-if="allOrderList.length">
								<view>
									<!-- 全部订单统计 -->
									<view class="all-orders-summary" v-if="orderSum">
										<view class="summary-item">
											<text class="summary-label">订单总数：</text>
											<text class="summary-value">{{ orderSum }}单</text>
										</view>
									</view>
									<view class="article" v-for="(item, index) in allOrderList" :key="index"
										:data-id="item.orderId" @click.stop="goOrderDetail">
										<!-- 订单头部信息 -->
										<view class="order-header">
											<view class="order-time">
												<image v-if="item.orderSourceImage" class="order-source-icon"
													:src="item.orderSourceImage"
													mode="scaleToFill" />
												<text v-if="item.mark" class="mark-text">{{ item.mark }}</text>
												<text class="time-text">下单时间：{{ item.orderDate }}</text>
											</view>
											<view class="all-orders-actions" v-if="item.applyBuck == 1">
												<view @click.stop="updateDeliveryInfo" :data-ordernum="item.orderNum" class="action-btn-small cancel-btn">
													撤销押桶
												</view>
											</view>
										</view>
										<!-- 客户信息和状态 -->
										<view class="customer-info">
											<view class="customer-main">
												<view class="customer-name-phone">
													<view v-if="item.isSend == 2" class="urge-badge">催单中</view>
													<text class="customer-name">{{ item.name }}</text>
													<text class="customer-phone">{{ item.phone }}</text>
												</view>
												<view class="action-buttons">
													<view @click.stop="refreshPhone(item.orderId)" class="action-btn refresh-btn">
														刷新电话
													</view>
													<view @click.stop="callPhone" :data-phone="item.phone" class="action-btn call-btn">
														打电话
													</view>
												</view>
											</view>
											<view class="customer-address">{{ item.underway }}</view>
										</view>
										<view v-if="item.applyBuck == 1" class="bucket-status-badge">
											送水员已发起押桶
										</view>
										<!-- 订单状态和操作按钮 -->
										<view class="order-status-actions">
											<view class="status-buttons">
												<view v-if="item.orderStatus == 1"
													class="status-btn status-btn-primary"
													:data-id="item.orderId"
													data-orderStatus="2"
													data-state="0"
													@click.stop="updateOrderState">
													接单
												</view>
												<view v-if="item.orderStatus == 2 && item.state == 0"
													class="status-btn status-btn-primary"
													:data-isalert="item.isAlert"
													:data-id="item.orderId"
													:data-userid="item.id"
													:data-ordernum="item.orderNum"
													data-type="0"
													@click.stop="choosePeopleAlert">
													固定送水员
												</view>
												<view v-if="item.orderStatus == 2 && item.state == 1"
													class="status-btn status-btn-warning"
													:data-id="item.orderId"
													data-orderStatus="2"
													data-state="0"
													data-cancel="1"
													@click.stop="updateOrderState">
													取消发单
												</view>
												<view v-if="item.orderStatus == 2 && item.state == 2 && !item.deliveryId"
													class="status-btn status-btn-primary"
													:data-id="item.orderId"
													data-orderStatus="3"
													data-state="1"
													@click.stop="updateOrderState">
													发货
												</view>
												<view v-if="item.orderStatus == 3 && item.state != 3"
													class="status-badge status-shipped">
													已发货
												</view>
												<view v-if="item.orderStatus == 3 && item.state == 3"
													class="status-badge status-signed">
													已签收
												</view>
												<view v-if="item.orderStatus == 10"
													class="status-badge status-completed">
													已完成
												</view>
												<view v-if="item.orderStatus == 9"
													class="status-badge status-reviewed">
													已评价
												</view>
												<view v-if="item.orderStatus == 6"
													class="status-badge status-cancelled">
													已取消
												</view>
												<view v-if="item.orderStatus == 7"
													class="status-badge status-rejected">
													已拒单
												</view>
												<view v-if="item.orderStatus == 8 && item.returnState == 1"
													class="status-badge status-return-pending">
													用户申请{{ item.returnName }}
												</view>
												<view v-if="item.orderStatus == 8 && item.returnState == 2"
													class="status-badge status-return-approved">
													已同意{{ item.returnName }}
												</view>
												<view v-if="item.orderStatus == 8 && item.returnState == 3"
													class="status-badge status-return-rejected">
													已拒绝{{ item.returnName }}
												</view>
											</view>
										</view>
										<!-- 商品信息简化显示 -->
										<view class="goods-info-simple">
											<view v-for="(it, imgIndex) in item.specList" :key="imgIndex" class="goods-item-simple">
												<text class="goods-name">{{ it.name || it.goodsName }}</text>
												<text class="goods-quantity">×{{ it.standardNumber }}</text>
											</view>
										</view>

										<!-- 订单金额信息 -->
										<view class="order-amount-info">
											<view class="amount-left">
												<view class="total-items">共{{ item.totalPieces }}件商品</view>
												<view v-if="item.transtatus != 3" class="amount-details">
													<view class="amount-row">
														<text class="amount-label">订单总额：</text>
														<text class="amount-value">￥{{ item.footing }}</text>
													</view>
													<view v-if="item.orderDiscounts != 0 && item.orderDiscounts" class="amount-row discount">
														<text class="amount-label">水票抵扣：</text>
														<text class="amount-value">-￥{{ item.orderDiscounts }}</text>
													</view>
													<view v-if="item.ticketPrice != '0.00' && item.ticketPrice" class="amount-row discount">
														<text class="amount-label">优惠券：</text>
														<text class="amount-value">-￥{{ item.ticketPrice }}</text>
													</view>
													<view v-if="item.yunfei != '0.00' && item.yunfei" class="amount-row">
														<text class="amount-label">配送费：</text>
														<text class="amount-value">+￥{{ item.yunfei }}</text>
													</view>
												</view>
												<view class="final-amount">
													<text class="final-label">实付金额：</text>
													<text class="final-value">￥{{ item.total }}</text>
												</view>
											</view>
											<view class="more-btn">
												<text>查看详情</text>
												<image :src="imgUri + '/images/jt-grey-right.png'" class="arrow-icon"></image>
											</view>
										</view>
										<!-- 配送员信息 -->
										<view class="delivery-info" v-if="item.deliveryName">
											<view class="delivery-person">
												<view class="delivery-detail">
													<text class="delivery-label">接单人姓名：</text>
													<text class="delivery-value">{{ item.deliveryName }}</text>
												</view>
												<view class="delivery-detail">
													<text class="delivery-label">接单人手机：</text>
													<text class="delivery-value">{{ item.deliveryPhone }}</text>
												</view>
											</view>
											<view v-if="item.orderStatus == 2 && item.state == 2" class="delivery-actions">
												<view class="action-btn-small confirm-btn"
													:data-order="item.orderId"
													:data-id="item.deliveryId"
													@click.stop="fixedDeliveryOrder">
													代接单
												</view>
												<view class="action-btn-small cancel-btn"
													:data-order="item.orderNum"
													:data-orderId="item.orderId"
													:data-id="item.deliveryId"
													@click.stop="cancelDriveryOrder">
													撤销派单
												</view>
											</view>
										</view>

										<!-- 操作按钮区域 -->
										<view class="action-buttons-area">
											<view v-if="item.affirm == 0"
												class="artTopRbtn actArtBtnBlue"
												:data-id="item.orderId"
												:data-userid="item.id"
												:data-ordernum="item.orderNum"
												data-type="0"
												@click.stop="confirmCollection">
												确认收款
											</view>
											<view v-else-if="item.affirm == 1"
												class="artTopRbtn actArtBtnGray">
												已收款
											</view>
											<!-- 催单发货按钮 -->
											<view class="artTopRbtn actArtBtnPurple"
												:data-id="item.orderId"
												@click.stop="urgeStore">
												催单发货
											</view>
											<!-- 操作记录按钮 -->
											<view class="artTopRbtn actArtBtnGreen"
												:data-ordernum="item.orderNum"
												@click.stop="showOrderLog">
												操作记录
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="no" v-if="allOrderList.length == '0'">
								<image :src="imgUri + '/images/noOrder.png'"></image>
								<view class="text">暂无需要处理的订单</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>
			</swiper>
			<!-- #ifdef MP-WEIXIN -->
			<!-- <image v-if="currentTab == 4" class="addBtn" :src="imgUri + '/images/addOrder.png'" @click.stop="addOrder"></image> -->
			<!-- #endif -->
			<!-- #ifndef MP-WEIXIN -->
			<!-- <image v-if="currentTab == 4" class="addBtn" style="bottom: 110rpx;" :src="imgUri + '/images/addOrder.png'"
			 @click.stop="addOrder"></image> -->
			<!-- #endif -->
			<!--切换内容部分end-->
		</view>

		<!-- 底部弹出选择送水员层 -->
		<view class="footerSelectShadow" v-if="isShow_01" :style="'height:' + winHeight + 'px'"></view>
		<view class="footerSelect" :style="'transform: translateY(' + translateY + 'rpx);'">
			<view class="flex align-items-center justify-content-between font-size-30 padding">
				<text @click.stop="cancleCallBack_01">取消</text>
				<text class="color-green" @click.stop="sureCallBack_01">确定</text>
			</view>
			<scroll-view scroll-y style="width:100%;height:400rpx;">
				<view class="driver-list">
					<view v-for="(item, index) in listData_01" :key="index" 
						class="driver-item" 
						:class="{'driver-item-selected': selectedDriverIndex === index}"
						@click="selectDriver(index)">
						{{ item }}
					</view>
				</view>
			</scroll-view>
		</view>


		<!-- 拍照确认弹窗 -->
		<view class="photo-confirm-modal" v-if="showPhotoConfirmModal">
			<view class="photo-confirm-mask" @click="handlePhotoClose"></view>
			<view class="photo-confirm-content">
				<!-- 关闭按钮 -->
				<view class="photo-confirm-close" @click="handlePhotoClose">
					<text class="close-icon">×</text>
				</view>
				<view class="photo-confirm-header">
					<image class="photo-confirm-image"
						src="https://waterstation.com.cn/szm/upload/20250401185034106.png" mode="aspectFit"></image>
				</view>
				<view class="photo-confirm-title">{{ store.isphoto == 1 ? '请进行拍照确认' : '是否需要拍照？' }}</view>
				<view class="photo-confirm-footer" :class="{ 'single-button': store.isphoto == 1 }">
					<!-- 只有当isphoto不等于1时才显示"不需要"按钮 -->
					<button v-if="store.isphoto != 1" class="photo-confirm-btn cancel" @click="handlePhotoCancel">不需要</button>
					<button class="photo-confirm-btn confirm" @click="handlePhotoConfirm">{{ store.isphoto == 1 ? '确认拍照' : '需要' }}</button>
				</view>
			</view>
		</view>

		<!-- 收款码弹窗 -->
		<view class="payment-code-modal" v-if="showPaymentCodeModal">
			<view class="payment-code-mask" @click="closePaymentCode"></view>
			<view class="payment-code-content">
				<view class="payment-code-header">
					<text class="payment-code-title">门店收款码</text>
					<view class="payment-code-close" @click="closePaymentCode">×</view>
				</view>
				<view class="payment-code-body">
					<image :show-menu-by-longpress="true" class="payment-code-img" :src="store.shoukuanma" mode="aspectFit"></image>
					<text class="payment-code-tips">出示给客户，进行收款</text>
				</view>
			</view>
		</view>

		<!-- 回退订单弹窗 -->
		<view class="back-order-modal" v-if="showBackOrderModal">
			<view class="back-order-mask" @click="closeBackOrderModal"></view>
			<view class="back-order-content">
				<view class="back-order-header">
					<text class="back-order-title">填写回退原因</text>
					<view class="back-order-close" @click="closeBackOrderModal">×</view>
				</view>
				<view class="back-order-body">
					<view class="back-order-form">
						<view class="form-item">
							<text class="form-label">回退原因</text>
							<textarea
								class="form-textarea"
								v-model="backOrderForm.reason"
								placeholder="请输入回退原因"
								maxlength="200"
								show-confirm-bar="false">
							</textarea>
						</view>
					</view>
				</view>
				<view class="back-order-footer">
					<button class="back-order-btn cancel" @click="closeBackOrderModal">取消</button>
					<button class="back-order-btn confirm" @click="submitBackOrder">确定</button>
				</view>
			</view>
		</view>

	<!-- 订单操作记录弹窗 -->
	<view v-if="showOrderLogModal" class="modal-overlay" @click="closeOrderLogModal">
		<view class="modal-content order-log-modal" @click.stop="">
			<view class="modal-header">
				<text class="modal-title">订单操作记录</text>
				<view class="modal-close" @click="closeOrderLogModal">×</view>
			</view>
			<view class="modal-body order-log-body">
				<view v-if="orderLogLoading" class="loading-container">
					<text class="loading-text">加载中...</text>
				</view>
				<view v-else-if="orderLogData && orderLogData.length > 0" class="order-log-content">
					<view class="log-item" v-for="(logItem, index) in orderLogData" :key="index">
						<view class="log-time">{{ logItem.createTime }}</view>
						<view class="log-content">{{ logItem.storeMsgModel }}</view>
						<view class="log-detail" v-if="logItem.content">{{ logItem.content }}</view>
					</view>
				</view>
				<view v-else class="no-log">
					<text class="no-log-text">暂无操作记录</text>
				</view>
			</view>
			<view class="modal-footer">
				<view class="modal-btn modal-btn-confirm" @click="closeOrderLogModal">关闭</view>
			</view>
		</view>
	</view>
</view>
</template>
<script>
// pages/orderAdmin/orderAdmin.js
import util from '../../utils/util.js';
import config from '../../utils/config.js';
import { transferStatus } from '../../utils/data.js';
var app = getApp();
var _this;
import navBar from '../../components/navBar/navBar';
import tabBar from '../../components/shopTab/shopTab';
export default {
	data() {
		return {
			transferStatus, // 转单状态映射
			searchName: '', // 搜索关键字
			sortField: 'orderDate', // 排序字段，默认按订单日期排序
			sortOrder: 'desc', // 排序方向，desc降序，asc新单在后
			searchTimer: null, // 搜索定时器
			// 下拉刷新相关
			refresherTriggered: false, // 下拉刷新状态
			isRefreshing: false, // 是否正在刷新，防止重复请求
			store: {},
			showPaymentCodeModal: false,
			showPhotoConfirmModal: false,
			showBackOrderModal: false,
			backOrderForm: {
				orderId: '',
				reason: ''
			},
			currentOrderInfo: {
				order: '',
				ordernumber: '',
				userid: ''
			},
			selfCount: 0,
			isDeal: 0,
			// 自定义Picker
			scrollTop: 0,
			temp: [],
			translateY: 600,
			isShow_01: false,
			listData_01: [],
			picker_01_data: [],
			picker_01_index: [],
			pickerValue: [0],
			peoples: ['cz', 'hz', 'yq', 'csl', 'pyw'],
			// 自定义Picker
			imgUri: this.$imgUri,
			tabs: ['待处理', '已完成', '手填订单'],
			itemList: ['待派单', '送水员待接单'],
			tabIndex: 0,
			list1: [],
			list2: [],
			itemListText: '待派单',
			itemListTextIndex: 0,
			stateArr: [{
				order: 1,
				state: 0
			}, //待接单
			{
				order: 2,
				state: 0
			}, //待发单
			{
				order: 2,
				state: 2
			}, //待发货
			// { order: 3, state: 2 },//已发货
			{
				order: 3,
				state: 3
			}
			],
			is_result: false,
			is_result1: false,
			is_result2: false,
			handOrderList: [],
			handCount: 0,
			page: 1,
			morePage: 0,
			showModal: true,
			p: 0,
			// 配送员业务费用
			l: 0,
			//楼层费用
			j: 0,
			//距离费用
			s: 0,
			//商品数量费用
			total: '0.00',
			flag: 0,
			// 0 发单 1 指派
			isHandOrder: true,
			// 手填订单 弹框
			pageSize: 5,
			isPageAdd: true,
			// 新切换 swiper
			// 全部订单、今日订单、待派单、已送达、手填订单、已完成
			// 2020-05-22 lcj // 水站待派单，自送订单，送水员待接单，已发货，已完成，押桶申请中，今日订单，全部订单
			switchtab: [{
				name: '待派单',
				_type: 'alreadyUsedMerchant',
				value: '6'
			},
			{
				name: '转单',
				_type: 'transferOrder',
				value: '10'
			},
			{
				name: '配送中',
				_type: 'alreadyUsedExpress',
				value: '8'
			},
			{
				name: '自送订单',
				_type: 'myDispatch',
				value: '7'
			},
			{
				name: '待退货',
				_type: 'wefqwegqwegqweg',
				value: '9'
			},
			{
				name: '已送达',
				_type: 'expired',
				value: '4'
			},
			{
				name: '押桶中',
				_type: 'expired',
				value: '5'
			},
			{
				name: '今日订单',
				_type: 'notUsed',
				value: '1'
			},
			{
				name: '全部订单',
				_type: 'allOrder',
				value: '0'
			},

			],
			currentTab: 0,
			coupons: [],
			// 新切换 swiper end
			isTarget: 0,
			//点击固定送水员跳转连点
			// 固定送水员
			formalDirvers: [],
			driverFlag: 0, //固定送水员指派节流
			orderingList: [],
			orverOrderList: [],
			orverOrderListCount: 0,
			allOrderList: [],
			orderSum: '',
			orderingListItem: '',
			alertGoodsNum: '',
			alertName: '',
			alertUnderway: '',
			alertOrderNum: '',
			alertUserID: '',
			isHaveSalesman: '',
			istype: '',
			deliveryInfoId: '',
			orderNum: '',
			winHeight: 0,

			scrollLeft: 0,
			winWidth: 0,
			// 今日订单
			todayOrderList: [],
			// 已送达
			songdaList: [],
			// 送水员已发起押桶
			yatongList: [],

			// 2020-05-22 lcj 新增
			// 6 水站待派单
			// 7 自送订单myDispatch
			myDispatchList: [],
			// 8 送水员待接单
			// 9 已发货订单 alreadyDelivery
			// alreadyDelivery: [],

			todaySum: 0,
			songdaSum: 0,
			yatongSum: 0,
			todayMoney: 0,
			songdaMoney: 0,
			typeTab: 0,
			szzs: "水站自送",

			editBucketInfo: null,
			afterSaleList: [],
			badgeList: {
				today: 0,
				yesterday: 0,
				all: 0,
				daiPaiDan: 0,
				daiJieDan: 0,
				huitui: 0,
				yiFaHuo: 0,
				yiQianShou: 0,
				yiWanCheng: 0,
				returnNum: 0,
				bucketNum: 0,
				pledgeBuckApplyNum: 0,
				selfCount: 0
			},
			selectedDriverIndex: -1,
			// 批量操作相关
			selectedOrders: [], // 选中的订单号数组
			isAllSelected: false, // 是否全选
			// 订单操作记录相关
			showOrderLogModal: false, // 是否显示操作记录弹窗
			orderLogData: null, // 操作记录数据列表
			orderLogLoading: false, // 操作记录加载状态
			currentOrderNum: '', // 当前查看的订单号
		};
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function () { },

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function () { },

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function () {
		uni.removeStorageSync("gotoDetailInfo");
	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function () {
		_this.setData({
			itemListText: _this.itemList[1] // currentTab: 0
		});

		// wx.removeStorageSync('target');
	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function () {
		let that = this;
		let refresh = uni.getStorageSync('refresh');
		if (!refresh) {

			uni.removeStorageSync('goodsList');
			uni.getSystemInfo({
				success(res) {
					console.log(res.windowHeight, '123');
					that.winHeight = res.windowHeight;
					that.winWidth = res.windowWidth - 30;
				},
				fail(fa) {
					console.log(fa);
				}
			});
			// 从订单详情返回时，不做处理
			if (uni.getStorageSync("gotoDetailInfo") && uni.getStorageSync("gotoDetailInfo") == 1) {
				uni.removeStorageSync("gotoDetailInfo");
				return;
			}

			if (that.typeTab) {
				let target = {
					order: 1,
					state: 0,
					index: 0
				};
				wx.setStorageSync('target', target);
			}
			// 进入订单页面默认显示今日订单 即 target 为空时，显示今日订单
			if (!wx.getStorageSync('target')) {
				let target = {
					order: 1,
					state: 0,
					index: 0
				};
				wx.setStorageSync('target', target);
			}
			// 2020-3-3 yxw 主页点击选项卡，跳转过来，多次会造成小程序，tab页来回切换 暂时注释，后期有问题，在解决
			// this.setData({
			// 	currentTab: 2
			// });
			// that.getHandOverList();
			that.load();
		}
		uni.removeStorageSync("refresh");
		// #ifdef APP-PLUS
		// 获取后台运行能力
		let g_wakelock = null;
		that.wakeLock();
		// #endif
			that.getUseDelivery();
			let storeInfo = uni.getStorageSync("storeInfo");
			that.store = storeInfo;
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function () { },

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function (options) {
		_this = this;
		if (options.type == 1) {
			_this.typeTab = options.type
		}
	},
	components: {
		navBar,
		tabBar
	},
	props: {},
	methods: {
		// 复制文本到剪贴板
		copyText(text) {
			if (!text) {
				uni.showToast({
					title: '内容为空',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: '复制成功',
						icon: 'success',
						duration: 2000
					});
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none',
						duration: 2000
					});
				}
			});
		},

		uploadpic(orderId) {
			uni.navigateTo({
				url: '/pagesMore/orderAdmin/uploadpic/uploadpic?orderId=' + orderId
			});
		},
		refreshPhone(orderId) {
			let that = this;
			util.ajax(config.refreshPhone, {
				orderId: orderId
			}, function (res) {
				if (res.data.code == 0) {
					util.showText('刷新成功');
					that.load();

				} else {
					util.showText(res.data.data);
					_this.setData({
						order: ''
					})
				}
			})
		},

		// 拨打电话
		callPhone(e) {
			uni.setStorageSync('refresh', '1')
			let phone = e.currentTarget.dataset.phone;
			uni.makePhoneCall({
				phoneNumber: phone + ''
			})
		},
		newGetOrderData(id) {
			// 2020-5-22 lcj
			// 方法集成了今日订单 压桶订单订单获取及分页处理方法
			let _this = this;
			let url = config.newGetOrder;
			let o = {
				storeId: app.globalData.storeId,
				state: id,
				pageSize: _this.pageSize,
				pageNo: _this.page
			};
			console.log('newGetOrderData', id);
			util.ajax(url, o, function (res) {
				if (res.data.code == 1) {
					let arr, arr1, arr2;
					if (res.data.data.list.length == 0) {
						if (_this.page != 1) {
							util.showText('已经到底啦~');
						}
						_this.setData({
							isPageAdd: false
						});
					}

					// 2020-05-22 lcj 只给今日订单和压桶订单做了处理
					// 第一次调用为第一页 arr直接赋值
					if (_this.page == 1) {
						arr = res.data.data.list;
					} else {
						// 2020-05-22 lcj
						// 若是翻页操作 arr1直接取已有的数据
						// 下一页数据赋值给arr2
						// 合并arr1 arr2 赋值给arr
						if (id == 1) {
							// 今日订单
							arr1 = _this.todayOrderList

						} else if (id == 3) {
							// 送水员已发起押桶
							arr1 = _this.yatongList
						}
						arr2 = res.data.data.list;
						if (arr2.length > 0) {
							arr = arr1.concat(arr2);
						} else {
							arr = arr1
						}
					}
					console.log(arr)
					if (id == 1) {
						// 今日订单
						_this.setData({
							todayOrderList: arr,
							todaySum: res.data.data.orderSum,
							todayMoney: res.data.data.todayPrice,
						});

					} else if (id == 3) {
						// 送水员已发起押桶
						_this.setData({
							yatongList: arr,
							yatongSum: res.data.data.buckNum
						});
					}
				} else {
					util.showText(res.data.data);
					_this.setData({});
				}
			});
		},

		bindChange(e) {
			console.log(e);
			let that = this;
			let index = e.detail.value;
			that.setData({
				pickerValue: index
			});

		},
		wakeLock: function () {
			//Android
			console.log('我是android');
			var main = plus.android.runtimeMainActivity();
			var Context = plus.android.importClass('android.content.Context');
			var PowerManager = plus.android.importClass('android.os.PowerManager');
			var pm = main.getSystemService(Context.POWER_SERVICE);
			g_wakelock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, 'ANY_NAME');
			g_wakelock.acquire();
		},
		aaa() {
			return '我是aaa';
		},
		//结束程序后台运行
		releaseWakeLock: function () {
			if (g_wakelock != null && g_wakelock.isHeld()) {
				g_wakelock.release();
				g_wakelock = null;
			}
		},
		refreshData() {
			// 如果正在刷新，直接返回，避免重复请求
			if (this.isRefreshing) {
				return;
			}
			this.load();
		},
		// 下拉刷新触发
		onRefresh() {
			// 如果正在刷新，直接返回，避免重复请求
			if (this.isRefreshing) {
				return;
			}

			this.isRefreshing = true;
			this.refresherTriggered = true;

			// 执行刷新逻辑
			this.load();

			// 延迟结束刷新状态，给用户反馈
			setTimeout(() => {
				this.refresherTriggered = false;
				this.isRefreshing = false;
			}, 800);
		},
		// 下拉刷新恢复
		onRestore() {
			this.refresherTriggered = false;
			this.isRefreshing = false;
		},
		load() {

			_this.page = 1;
			_this.page = 1;
			_this.isPageAdd = true;
			(_this.orderingList = []), (_this.orverOrderList = []), (_this.allOrderList = []), (_this.handOrderList = []), (_this.afterSaleList = []),
				// 2020-05-22 lcj 新增
				// 6 水站待派单
				// 7 自送订单myDispatch
				(_this.myDispatchList = []);
			// 8 送水员待接单
			// 9 已发货订单 alreadyDelivery
			// (_this.alreadyDelivery = []);

			// 2020-5-22 lcj 待派单状态拆分到navTab
			// if (_this.switchtab[_this.currentTab].value == '2') {
			// 	_this.getOrderingList();
			// } else

			if (_this.switchtab[_this.currentTab].value == '4') {
				_this.getOverOrderList();
			} else if (_this.switchtab[_this.currentTab].value == '0') {
				// 0
				_this.getAllOrderList();
			} else if (_this.switchtab[_this.currentTab].value == '1') {
				// 今日订单
				_this.newGetOrderData(1);
			} else if (_this.switchtab[_this.currentTab].value == '3') {
				_this.newGetOrderData(2);
			} else if (_this.switchtab[_this.currentTab].value == '5') {
				_this.newGetOrderData(3);
				// 新增四个列表
			} else if (_this.switchtab[_this.currentTab].value == '6') {
				_this.getOrderingList(1);
			}
			else if (_this.switchtab[_this.currentTab].value == '10') {
				_this.getTransferOrderList();
			}
			else if (_this.switchtab[_this.currentTab].value == '7') {
				_this.getMyDispatchList();
			}
			else if (_this.switchtab[_this.currentTab].value == '8') {
				_this.getOrderingList(2);
			}
			else if (_this.switchtab[_this.currentTab].value == '9') {
				_this.getafterSaleList();
			}
			_this.orderTongji();
		},
		sureCallBack_01(e) {
			// let data = e.detail,
			// 	_this = this;
			let that = this;
			let list = that.listData_01;
			let index = that.selectedDriverIndex;
			let name = [list[index]];
			this.setData({
				isShow_01: false,
				translateY: 600,
				picker_01_data: name,
				picker_01_index: JSON.stringify(that.pickerValue)
			});

			if (name == that.szzs) {
				//水站自送
				that.merchantDeliverSelf();
			} else {
				that.chooseDriver();
			}


		},
		selectDriver(e) {
			this.selectedDriverIndex = e;
			console.log(e);
		},
		cancleCallBack_01() {
			this.setData({
				isShow_01: false,
				pickerValue: [0],
				translateY: 600
			});
		},

		//选择固定送水员
		chooseDriver(e) {
			let that = this;
			let driverData = that.picker_01_data;
			let list = that.formalDirvers;

			if (that.driverflag == 1) {
				util.alert('请勿重复操作');
				return;
			}

			that.setData({
				driverflag: 1
			});
			setTimeout(function () {
				that.setData({
					driverflag: 0
				});
			}, 2000);

			// 检查是否是批量操作（订单号包含逗号）
			let orderNums = that.orderNum.split(',');
			let isBatchOperation = orderNums.length > 1;

			// 找到选中的送水员ID
			let deliveryId = '';
			for (var i in list) {
				if (list[i].name == driverData) {
					deliveryId = list[i].id;
					break;
				}
			}

			if (!deliveryId) {
				uni.showToast({
					title: '请选择送水员',
					icon: 'none'
				});
				return;
			}

			console.log('批量派单:', { orderNums, deliveryId, isBatchOperation });

			uni.showLoading({
				title: isBatchOperation ? `批量派单中...` : '加载中...'
			});

			if (isBatchOperation) {
				// 批量派单处理
				that.processBatchDispatch(orderNums, deliveryId);
			} else {
				// 单个订单派单处理
				var o = {
					orderId: that.orderNum,
					deliveryId: deliveryId
				};
				util.ajax(config.selectdelivery, o, function (res) {
					uni.hideLoading();
					if (res.data.code == 1) {
						uni.showToast({
							title: '派单成功',
							icon: 'success'
						});
						// 刷新列表
						setTimeout(() => {
							that.load();
						}, 1000);
					} else {
						uni.showToast({
							title: res.data.data || '派单失败',
							icon: 'none'
						});
					}
				});
			}
		},

		// 批量派单处理方法
		processBatchDispatch(orderNums, deliveryId) {
			const that = this;
			let successCount = 0;
			let failCount = 0;
			let totalCount = orderNums.length;

			// 递归处理每个订单
			const processOrder = (index) => {
				if (index >= orderNums.length) {
					// 所有订单处理完成
					uni.hideLoading();
					if (failCount === 0) {
						uni.showToast({
							title: `批量派单完成！成功处理 ${successCount} 个订单`,
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: `批量派单完成！成功 ${successCount} 个，失败 ${failCount} 个`,
							icon: 'none'
						});
					}
					// 清空选中的订单
					that.clearSelection();
					// 刷新列表
					that.load();
					return;
				}

				// 处理当前订单
				let orderNum = orderNums[index];
				let params = {
					orderId: orderNum,
					deliveryId: deliveryId
				};

				util.ajax(config.selectdelivery, params, function(res) {
					if (res.data.code == 1) {
						successCount++;
					} else {
						failCount++;
					}

					// 处理下一个订单
					processOrder(index + 1);
				});
			};

			// 开始处理第一个订单
			processOrder(0);
		},




		// 代 替送水员接单 fixedDeliveryOrder
		fixedDeliveryOrder(e) {
			var _this = this,
				order = e.currentTarget.dataset.order,
				id = e.currentTarget.dataset.id;
			var o = {
				orderId: order,
				userId: id,
				latAndLon: wx.getStorageSync('storeLocal')
			};
			util.ajax(config.fixedDeliveryOrder, o, function (res) {
				if (res.data.code == 1) {
					wx.showToast({
						title: '接单成功',
						success() {
							setTimeout(function () {
								// _this.setData({
								// 	currentTab: 2
								// })
								_this.load();
							}, 1000);
						}
					});
				} else {
					wx.showToast({
						title: res.data.data,
						icon: 'none',

						success() {
							setTimeout(function () {
								// _this.setData({
								// 	currentTab: 2
								// })
								_this.load();
							}, 1000);
						}
					});
				}
			});
		},

		// 撤销指派 cancelDriveryOrder
		cancelDriveryOrder(e) {
			var _this = this,
				order = e.currentTarget.dataset.order,
				orderId = e.currentTarget.dataset.orderid,
				id = e.currentTarget.dataset.id;

			util.ajax(
				config.cancelDriveryOrder, {
				orderNum: order,
				orderId: orderId,
				deliveryId: id
			},
				function (res) {
					if (res.data.code == 1) {
						wx.showToast({
							title: '撤销成功',
							success() {
								setTimeout(function () {

									_this.load();
								}, 1000);
							}
						});
					} else {
						wx.showToast({
							title: res.data.data,
							icon: 'none',
							success() {
								setTimeout(function () {

									_this.load();
								}, 1000);
							}
						});
					}
				}
			);
		},
		// 2020-05-23 lcj
		// 自送订单相关
		// 自送单修改为自送订单
		merchantDeliverSelf() {
			var that = this;
			var o = {
				orderNumber: that.orderNum,
				latAndLon: wx.getStorageSync('storeLocal')
			};
			wx.showLoading({
				title: '加载中...'
			});
			util.ajax(config.merchantDeliverSelf, o, function (res) {
				wx.hideLoading();
				if (res.data.code == 1) {
					wx.showToast({
						title: '操作成功',

						success() {
							setTimeout(function () {
								that.load();
							}, 1000);
						}
					});
				} else {
					wx.showToast({
						title: res.data.data,
						icon: 'none',

						success() {
							setTimeout(function () {
								that.load();
							}, 1000);
						}
					});
				}
			});

		},

		merchantDeliverSelfComplete(e) {
			var order = e.currentTarget.dataset.id;
			var ordernumber = e.currentTarget.dataset.ordernum;
			var userid = e.currentTarget.dataset.userid;
			if (this.store.isphoto == 1) {
				
				this.currentOrderInfo = {
					order,
					ordernumber,
					userid
				}
				this.handlePhotoConfirm();
			} else {
			// 使用自定义弹窗
			this.setData({
				showPhotoConfirmModal: true,
				currentOrderInfo: {
					order,
					ordernumber,
					userid
				}
			});
			}
		},
		handlePhotoCancel() {
			// 如果store设置为强制拍照，则不允许取消
			if (this.store.isphoto == 1) {
				uni.showToast({
					title: '当前设置为强制拍照，无法跳过',
					icon: 'none'
				});
				return;
			}

			let target = {
				order: 1,
				state: 0,
				index: 1
			};
			wx.setStorageSync('target', target);
			this.setData({
				showPhotoConfirmModal: false
			});


			uni.showLoading({
				title: "加载中..."
			});
			var o = {
				orderId: this.currentOrderInfo.order
			}
			util.ajax(config.eidtDriverState, o, function (res) {
				uni.hideLoading();
				if (res.data.code == 1) {

					wx.showToast({
						title: "操作成功",
					});
					setTimeout(function () {
						if (res.data.data && res.data.data == 3) {
							_this.load();
						} else {
							// 没有押桶，跳到退桶回桶页面
							wx.navigateTo({
								url:
									"/pagesMore/userAdmin/bucketPage/bucketPage?showPay=1&orderNumber=" +
									_this.currentOrderInfo.ordernumber +
									"&userId=" +
									_this.currentOrderInfo.userid +
									"&isuser=0",
							});
						}
					}, 500);

				} else {
					wx.showToast({
						title: res.data.data,
						icon: 'none',
						success() {
							setTimeout(function () {
								_this.load();
							}, 500);
						}
					});
				}
			});
		},
		handlePhotoConfirm() {
			this.setData({
				showPhotoConfirmModal: false
			});
			wx.navigateTo({
				url:
					"/pagesMore/orderAdmin/uploadpic/uploadpic?orderId=" +
					this.currentOrderInfo.order +
					"&ordernumber=" +
					this.currentOrderInfo.ordernumber +
					"&userid=" +
					this.currentOrderInfo.userid +
					"&flag=1",
			});



		},

		// 关闭拍照弹窗
		handlePhotoClose() {
			// 如果是强制拍照模式，给出提示但仍允许关闭
			if (this.store.isphoto == 1) {
				uni.showModal({
					title: '提示',
					content: '当前设置为强制拍照，确定要关闭吗？',
					success: (res) => {
						if (res.confirm) {
							this.setData({
								showPhotoConfirmModal: false
							});
						}
					}
				});
			} else {
				// 非强制拍照模式直接关闭
				this.setData({
					showPhotoConfirmModal: false
				});
			}
		},
		backOrder(e) {
			var _this = this,
				order = e.currentTarget.dataset.id;
			uni.showLoading({
				title: "加载中..."
			});
			var o = {
				orderId: order
			}
			util.ajax(config.szmsendmembercontrollerback, o, function (res) {
				uni.hideLoading();
				if (res.data.code == 1) {
					_this.load();

				} else {
					wx.showToast({
						title: res.data.data,
						icon: 'none',
						success() {
							setTimeout(function () {
								_this.load();
							}, 1000);
						}
					});
				}
			});

		},
		// 自送单不回桶
		noBackBucket(e) {
			console.log(e.currentTarget.dataset.id)
			let _this = this
			let o = {
				orderId: e.currentTarget.dataset.id,
				header: true
			}
			wx.showModal({
				title: '温馨提示',
				content: '您是否确认客户本次订单不回桶？',

				success(res) {
					if (res.confirm) {
						util.ajax(config.finishOrder, o, function (res) {
							if (res.data.code == 1) {
								setTimeout(function () {
									_this.load();
								}, 1000);
							} else {
								wx.showToast({
									title: res.data.data,
									icon: 'none',
									success() {
										setTimeout(function () {
											_this.load();
										}, 1000);
									}
								});
							}
						});
					}
				}
			});
		},
		// 自送单回桶
		goBackBucket(e) {
			let _this = this
			let order = e.currentTarget.dataset.ordernum
			let userId = e.currentTarget.dataset.userid
			let target = {
				order: 1,
				state: 0,
				index: 1
			};
			wx.setStorageSync('target', target);
			util.reto(`/pagesMore/userAdmin/bucketPage/bucketPage?type=htd&userId=${userId}&gethz=gethz&orderNumber=` + order);
		},
		// 自送单水站补压桶
		goMendBucket(e) {
			var _this = this,
				order = e.currentTarget.dataset.ordernum,
				userId = e.currentTarget.dataset.userid;
			let url = config.selectdebtdeatil;
			let o = {
				orderNumber: order,
				index: 1,
				pageSize: 10000,
				userId: userId,
			}

			let target = {
				order: 1,
				state: 0,
				index: 1
			};
			wx.setStorageSync('target', target);
			util.ajax(url, o, function (res) {
				if (res.data.code == 1) {
					if (res.data.data.length == 0) {
						util.showText('该订单下暂无可押桶信息')
						return
					}
					util.reto(`/pages/orderAdmin/mendBucket/mendBucket?userid=${userId}&ordernum=${order}`);
				}
			})
		},

		goKeHuBucket(e) {
			var _this = this;
			var order = e.currentTarget.dataset.ordernum;
			let url = config.updatedeliveryinfo;
			let o = {
				orderNumber: order,
				state: 1
			}
			util.ajax(url, o, function (res) {
				if (res.data.code == 1) {
					util.showText('押桶申请提交成功，请提醒客户进行押桶！')
				} else {
					util.showText(res.data.data);
				}
			})
		},

		// 自定义picker
		lower(e) {
			console.log(e);
			console.log(_this.itemListText)
			var pageSize = _this.page;

			if (_this.isPageAdd) {
				pageSize = pageSize + 1;
			}
			_this.page = pageSize;
			console.log(_this.page, 'isPageAdd');
			// 0全部订单 1今日订单 2待派单 3已送达 4已完成 5送水员已发起押桶
			// 2020-5-22 lcj
			//  6水站待派单 7自送订单 8送水员待接单 9已发货

			if (_this.switchtab[_this.currentTab].value == '4') {
				_this.getOverOrderList();
			} else if (_this.switchtab[_this.currentTab].value == '0') {
				// 0
				_this.getAllOrderList();
			} else if (_this.switchtab[_this.currentTab].value == '1') {
				// 今日订单
				_this.newGetOrderData(1);
			} else if (_this.switchtab[_this.currentTab].value == '5') {
				_this.newGetOrderData(3);
			} else if (_this.switchtab[_this.currentTab].value == '6') {
				_this.getOrderingList(1);
			}
			else if (_this.switchtab[_this.currentTab].value == '10') {
				_this.getTransferOrderList();
			}
			else if (_this.switchtab[_this.currentTab].value == '7') {
				_this.getMyDispatchList();
			}
			else if (_this.switchtab[_this.currentTab].value == '8') {
				_this.getOrderingList(2);
			}
			else if (_this.switchtab[_this.currentTab].value == '9') {
				_this.getafterSaleList();
			}
		},

		/**
		 * 弹出框蒙层截断touchmove事件
		 */
		//tab切换函数，让swiper当前滑块的current的index与tab头部index一一对应
		switchNav: function (e) {
			var index = e.target.dataset.current;
			if (this.currentTab == index) {
				return false;
			} else {
				// 清空批量选择状态
				this.clearSelection();

				// 重置刷新状态
				this.refresherTriggered = false;
				this.isRefreshing = false;

				this.setData({
					currentTab: index,
					// itemListText: this.itemList[0]
				});
			}
			_this.page = 1;
		},

		//滑动swiper切换，让swiper当前滑块的current的index与tab头部index一一对应
		tabChange(e) {
			// 清空批量选择状态
			this.clearSelection();

			// 重置刷新状态
			this.refresherTriggered = false;
			this.isRefreshing = false;

			this.setData({
				currentTab: e.detail.current,
				// itemListText: this.itemList[0]
			});
			var index = e.detail.current;
			_this.page = 1;
			_this.isPageAdd = true;

			_this.orderingList = []
			_this.orverOrderList = []
			_this.allOrderList = []
			_this.handOrderList = []
			_this.yatongList = []
			_this.todayOrderList = []
			_this.myDispatchList = []
			_this.afterSaleList = []

			// // 0全部订单 1今日订单 2待派单 3已送达 4手填订单 5已完成 6送水员已发起押桶
			// change 2020-3-3 yxw // 0全部订单 1今日订单 2待派单 3已完成 4送水员已发起押桶
			// 2020-05-22 lcj
			// 6水站待派单，7自送订单，8送水员待接单，9已发货，3已完成 ，4送水员已发起押桶，1今日订单，0全部订单

			if (_this.switchtab[index].value == '4') {
				_this.getOverOrderList();
			} else if (_this.switchtab[index].value == '0') {
				// 0
				_this.getAllOrderList();
			} else if (_this.switchtab[index].value == '1') {
				// 今日订单
				_this.newGetOrderData(1);

			} else if (_this.switchtab[index].value == '5') {
				_this.newGetOrderData(3);
			} else if (_this.switchtab[index].value == '6') {
				_this.getOrderingList(1);
			}
			else if (_this.switchtab[index].value == '10') {
				_this.getTransferOrderList();
			}
			else if (_this.switchtab[index].value == '7') {
				_this.getMyDispatchList();
			}
			else if (_this.switchtab[index].value == '8') {
				_this.getOrderingList(2);
			}
			else if (_this.switchtab[index].value == '9') {
				_this.getafterSaleList();
			}
		},

		preventTouchMove: function () { },

		hideModal() {
			var that = this;
			that.setData({
				showModal: true
			});
		},

		handhideModal() {
			var that = this;
			that.setData({
				isHandOrder: true
			});
		},

		//调用接口 根据输入的地址 获取经纬度
		getLocation(address, ordernum) {
			var _this = this; //调用地址解析接口

			wx.showLoading({
				title: '加载中...'
			});
			app.qqmapsdk.geocoder({
				//获取表单传入地址
				address: address,
				//地址参数，例：固定地址，address: '北京市海淀区彩和坊路海淀西大街74号'
				success: function (res) {
					//成功后的回调
					console.log(res, '我是地址解析');
					var res = res.result;
					var latitude = res.location.lat;
					var longitude = res.location.lng;
					var f = latitude + ',' + longitude;
					var to = wx.getStorageSync('storeLocal');

					_this.getRoadLine(f, to, ordernum);
				},
				fail: function (error) {
					console.error(error);
				}
			});
		},

		getRoadLine(f, t, ordernum) {
			console.log(f, '==', t, '==', ordernum);

			var _this = this; //调用距离计算接口

			app.qqmapsdk.direction({
				mode: 'driving',
				//可选值：'driving'（驾车）、'walking'（步行）、'bicycling'（骑行），不填默认：'driving',可不填
				//from参数不填默认当前地址
				from: f,
				// 起点
				to: t,
				// 终点
				success: function (res) {
					console.log(res); // _this.selectdeliveryrule(ordernum, res.result.routes[0].distance);

					_this.setData({
						distance: Number(res.result.routes[0].distance / 1000).toFixed(1),
						showModal: false
					});

					wx.hideLoading();
				},
				fail: function (error) {
					console.error(error);
					wx.hideLoading();
				},
				complete: function (res) {
					console.log(res);
				}
			});
		},

		// 更新订单状态
		updateOrderState(e) {
			var that = this;
			var datas = e.currentTarget.dataset,
				type = e.currentTarget.dataset.type,
				orderingList = that.orderingList,
				isAlert = '';

			for (var i in orderingList) {
				if (datas.ordernum == orderingList[i].orderNum) {
					that.setData({
						orderingListItem: orderingList[i],
						alertGoodsNum: orderingList[i].totalPieces,
						alertName: orderingList[i].name,
						alertUnderway: orderingList[i].underway
					});

					isAlert = orderingList[i].isAlert;
					var local = orderingList[i].underway.split(' ');

					if (isAlert == 1) {
						that.getLocation(local[0], '');
					}
				}
			}

			if (isAlert == 1) {
				that.setData({
					showModal: false,
					flag: 0,
					uporderId: datas.id,
					uporderStatus: datas.orderstatus,
					upstate: datas.state
				});

				return;
			}

			var o = {
				orderId: datas.id,
				orderStatus: datas.orderstatus,
				state: datas.state
			};

			if (datas.orderstatus == 2 && datas.state == 0 && datas.cancel == 1) {
				wx.showModal({
					title: '温馨提示',
					content: '该订单已发布到派单大厅，您确定要取消发单吗？',

					success(res) {
						if (res.confirm) {
							util.ajax(config.updateOrderState, o, function (res) {
								if (res.data.code == 1) {
									if (that.currentTab == 0) {
										that.getAllOrderList();
									} else {
										that.load();
									}
								} else {
									util.showText(res.data.data);
								}
							});
						}
					}
				});
			} else {
				if (datas.orderstatus != undefined) {
					console.log('我是');
					util.ajax(config.updateOrderState, o, function (res) {
						if (res.data.code == 1) {
							util.showSuccess('操作成功');
							that.load();
						} else {
							util.showText(res.data.data);
						}
					});
				}
			}
		},

		// 订单详情
		goOrderDetail(e) {
			var id = e.currentTarget.dataset.id;
			uni.setStorageSync("gotoDetailInfo", 1);
			uni.navigateTo({
				url: '/pages/orderAdmin/orderDetail/orderDetail?id=' + id
			});
		},
		goTranDetail(e) {
			var id = e.currentTarget.dataset.id;
			uni.navigateTo({
				url: '/pagesMore/mine/exchangeOrder/detail/detail?id=' + id
			});
		},

		// 显示订单操作记录
		showOrderLog(e) {
			const orderNum = e.currentTarget.dataset.ordernum;
			if (!orderNum) {
				uni.showToast({
					title: '订单号不能为空',
					icon: 'none'
				});
				return;
			}

			this.currentOrderNum = orderNum;
			this.showOrderLogModal = true;
			this.getOrderLog(orderNum);
		},

		// 获取订单操作记录
		getOrderLog(orderNum) {
			const that = this;
			that.orderLogLoading = true;
			that.orderLogData = null;

			util.ajax(config.selectMsgByOrderNum, {
				orderNum: orderNum
			}, function(res) {
				that.orderLogLoading = false;
				if (res.data.code == 1) {
					// 如果返回的是单个对象，转换为数组
					if (res.data.data && !Array.isArray(res.data.data)) {
						that.orderLogData = [res.data.data];
					} else {
						that.orderLogData = res.data.data || [];
					}
				} else {
					that.orderLogData = null;
					if (res.data.data !== '暂无数据') {
						uni.showToast({
							title: res.data.data || '获取操作记录失败',
							icon: 'none'
						});
					}
				}
			});
		},

		// 关闭订单操作记录弹窗
		closeOrderLogModal() {
			this.showOrderLogModal = false;
			this.orderLogData = null;
			this.orderLogLoading = false;
			this.currentOrderNum = '';
		},

		// 总价求和
		sumTotal(i, j, l, s, p) {
			var total = 0,
				p = p || 0;

			if (i == 1) {
				total = Number(Number(p) + Number(j) + Number(l) + Number(s)).toFixed(2);
			} else {
				total = Number(Number(j) + Number(l) + Number(s)).toFixed(2);
			}

			return total;
		},

		// 获取输入框的值 getInputVal
		getInputVal(e) {
			var name = e.currentTarget.dataset.name,
				val = e.detail.value,
				total = 0,
				isHaveSalesman = _this.isHaveSalesman;

			_this.setData({
				[name]: e.detail.value
			});

			if (isHaveSalesman == 1) {
				total = _this.sumTotal(isHaveSalesman, _this.j, _this.l, _this.s, _this.p);

				_this.setData({
					total: total
				});
			} else {
				total = _this.sumTotal(isHaveSalesman, _this.j, _this.l, _this.s, _this.p);

				_this.setData({
					total: total
				});
			}
		},

		// 获取是否设置过规则
		selectdeliveryrule(orderNum, discount) {
			var isHaveSalesman = '',
				orderingList = '';

			if (_this.istype == 0) {
				orderingList = _this.orderingList;
			} else {
				orderingList = _this.orverOrderList;
			}

			for (var i in orderingList) {
				if (orderNum == orderingList[i].orderNum) {
					isHaveSalesman = orderingList[i].isHaveSalesman;
				}
			}

			util.ajax(
				config.selectdeliveryrule, {
				orderNumber: orderNum,
				discount: discount
			},
				function (res) {
					if (res.data.code == 1) {
						var isHaveRule = res.data.data,
							total = 0;

						var total = _this.sumTotal(isHaveSalesman, isHaveRule.discountMoneyt, isHaveRule.floormoney, isHaveRule.shopNumberMoney,
							isHaveRule.deliveryMoney);

						_this.setData({
							showModal: false,
							isHaveRule: res.data.data,
							total: total,
							isHaveSalesman: isHaveSalesman,
							p: isHaveRule.deliveryMoney,
							l: isHaveRule.floormoney,
							s: isHaveRule.shopNumberMoney,
							j: isHaveRule.discountMoneyt
						});

						console.log(_this.isHaveSalesman, 'saddsadsa');
						console.log(_this.isHaveRule, '我是大');
					} else {
						_this.setData({
							total: '0.00'
						});

						_this.setData({
							showModal: false,
							isHaveRule: res.data.data,
							p: '0.00',
							l: '0.00',
							s: '0.00',
							j: '0.00'
						});

						console.log(_this.isHaveRule, '我是大12');
					}
				}
			);
		},

		// 发单函数
		sendOrder() {
			var o = {
				orderId: _this.uporderId,
				orderStatus: _this.uporderStatus,
				state: _this.upstate,
				total: _this.total,
				// 提成总额
				comeUpFee: _this.l,
				//楼层提成
				distanceFee: _this.j,
				// 距离提成
				numberFee: _this.s //数量提成
			};
			if (_this.total <= 0) {
				util.showText('提成金额不能为空');
				return;
			}
			if (_this.uporderStatus != undefined) {
				util.ajax(config.updateOrderState, o, function (res) {
					if (res.data.code == 1) {
						util.showSuccess('发单成功');

						_this.setData({
							showModal: true
						});

						setTimeout(function () {
							_this.load();
						}, 1000);
					} else {
						util.showText(res.data.data);
					}
				});
			}
		},

		// 执行发单和指派操作 设置完毕提成
		submitInfo(e) {
			var flag1 = e.currentTarget.dataset.flag;
			var flag = _this.flag;

			if (flag1) {
				flag = flag1;
			}

			console.log(flag);

			if (flag == 0) {
				// 发单操作
				_this.sendOrder();
			} else if (flag == 1) {
				// 立即设置 指派操作\
				var orderingListItem = _this.orderingListItem,
					data = '';
				var o = {
					orderId: _this.alertOrderNum,
					deliveryId: '',
					comeUpFee: _this.l,
					distanceFee: _this.j,
					numberFee: _this.s,
					business: _this.p
				};
				console.log(o);

				_this.setData({
					showModal: true
				});

				wx.setStorageSync('choosePeople', o);
				util.reto('/pages/orderAdmin/choosePeople/choosePeople');
			}
		},

		choosePeopleAlert(e) {
			var userid = e.currentTarget.dataset.userid,
				_this = this,
				isAlert = e.currentTarget.dataset.isalert,
				orderNum = e.currentTarget.dataset.ordernum;

			_this.setData({
				isShow_01: true,
				translateY: 0,
				orderNum: orderNum
			});
		},

		// 选择指派的人员
		choosePeople(e) {
			if (_this.isTarget == 1) {
				return;
			}

			console.log(123);
			_this.isTarget = 1;
			setTimeout(function () {
				_this.isTarget = 0;
			}, 2000);
			var datas = e.currentTarget.dataset,
				type = e.currentTarget.dataset.type,
				orderingList = '',
				isHaveRule = '',
				isAlert = '';

			if (type == 0) {
				orderingList = _this.orderingList;

				_this.setData({
					flag: 1
				});
			} else {
				orderingList = _this.orverOrderList;

				_this.setData({
					flag: 2
				});
			}

			for (var i in orderingList) {
				if (datas.ordernum == orderingList[i].orderNum) {
					_this.setData({
						orderingListItem: orderingList[i],
						alertGoodsNum: orderingList[i].totalPieces,
						alertName: orderingList[i].name,
						alertUnderway: orderingList[i].underway,
						alertOrderNum: orderingList[i].orderNum,
						alertUserID: orderingList[i].id,
						isHaveSalesman: orderingList[i].isHaveSalesman,
						istype: type,
						deliveryInfoId: orderingList[i].deliveryInfoId || ''
					});

					console.log(_this.isHaveSalesman, 'w shi dshidasfa');
					var local = orderingList[i].underway.split(' ');
					isAlert = orderingList[i].isAlert;

					if (isAlert == 1) {
						_this.getLocation(local[0], datas.ordernum);
					}
				}
			}

			if (isAlert != 1) {
				var o = {
					orderId: _this.alertOrderNum
				};
				wx.setStorageSync('choosePeople', o);

				_this.setData({
					showModal: true
				}); // \
				// _this.chooseDriver();
				// return;
				// util.reto("/pages/orderAdmin/choosePeople/choosePeople")
			}
		},

		editHandOrder(e) {
			let that = this;
			let order = e.currentTarget.dataset.order;
			util.reto('/pages/orderAdmin/addOrder/addOrder?order=' + order);
		},

		// 获取可接单的送水员
		getUseDelivery() {
			// 固定送水员
			let that = this;
			util.ajax(
				config.getShopAllDelivery, {
				storeId: app.globalData.storeId
			},
				function (res) {
					if (res.data.code == 1) {
						var list = res.data.data,
							listData = [that.szzs];

						for (var i in list) {
							listData.push(list[i].name);
						}

						_this.setData({
							formalDirvers: res.data.data,
							listData_01: listData
						});

					} else {
						listData = [that.szzs];
						_this.setData({
							formalDirvers: [],
							listData_01: listData
						});
					}
				}
			);
		},

		// 查看所有订单 getAllOrderList
		getAllOrderList() {
			// allOrderList
			var that = this;
			util.ajax(
				config.allOrderList, {
				storeId: app.globalData.storeId,
				pageSize: that.page
			},
				function (res) {
					if (res.data.code == 1) {
						if (res.data.data.list.length == 0) {
							if (that.page != 1) {
								util.showText('已经到底啦~');
							}
							_this.setData({
								isPageAdd: false
							});
						}

						if (that.page == 1) {
							_this.setData({
								allOrderList: res.data.data.list,
								orderSum: res.data.data.orderSum
							});
						} else {
							var arr = res.data.data.list;
							var arr1 = _this.allOrderList;
							var arr2 = arr1.concat(arr);

							_this.setData({
								allOrderList: arr2,
								orderSum: res.data.data.orderSum
							});
						}
					} else {
						_this.setData({
							allOrderList: '',
							orderSum: ''
						});
					}
				}
			);
		},
		orderTongji() {
			// allOrderList
			util.ajax(
				config.allOrderListPcanalysis, {
				storeId: app.globalData.storeId,
				pageNo: 1,
				pageSize: 1,
				state: -1,
				orderStatus: -1,
			},
				function (res) {
					if (res.data.code == 1) {
						_this.setData({
							badgeList: res.data.data
						});
					} else {
						_this.setData({
							badgeList: {}
						});
					}
				}
			);
		},

		// 自送订单
		getMyDispatchList() {
			// getMyDispatchList
			var that = this;
			util.ajax(
				config.selfList, {
				storeId: app.globalData.storeId,
				pageNo: that.page,
				pageSize: 5,
				orderStatus: 4,
				state: 2
			},

				function (res) {
					if (res.data.code == 1) {
						if (res.data.data.userList.length == 0) {
							if (that.page != 1) {
								util.showText('已经到底啦~');
							}
							_this.setData({
								isPageAdd: false
							});
						}

						// 初始化选择状态
						let userList = res.data.data.userList;
						userList.forEach(item => {
							item.isSelected = false;
						});

						if (that.page == 1) {
							_this.setData({
								myDispatchList: userList,
								orderSum: res.data.data.orderSum,
								selfCount: res.data.data.count,
								selectedOrders: [], // 重置选中状态
								isAllSelected: false
							});
						} else {
							var arr = userList;
							var arr1 = _this.myDispatchList;
							var arr2 = arr1.concat(arr);

							_this.setData({
								myDispatchList: arr2,
								orderSum: res.data.data.orderSum,
								selfCount: res.data.data.count,
							});
						}
					} else {
						_this.setData({
							myDispatchList: '',
							orderSum: ''
						});
					}
				}
			);
		},

		// 待处理列表
		getOrderingList(type) {
			_this.setData({
				is_result: true
			});

			util.ajax(
				config.getOrderByStateNew, {
				storeId: app.globalData.storeId,
				state: type,
				pageSize: _this.pageSize,
				pageNo: _this.page,
				name: _this.searchName, // 添加搜索关键词
				sortField: _this.sortField, // 添加排序字段
				sortOrder: _this.sortOrder // 添加排序方向
			},
				function (res) {
					if (res.data.code == 1) {
						console.log(res.data, '原始订单数据列表')
						let orderList = res.data.data.list
						//查询是否有转单
						let orderIds = orderList.map(item => item.orderId)
						uni.request({
							url: config.queryOrderExchangeStatusApi,
							method: 'POST',
							header: {
								"Content-Type": "application/json"
							},
							data: {
								orderMainIdList: orderIds
							},
							success: (exRes) => {
								if (exRes.data.code == 1) {
									let exOrderStatusMap = exRes.data.data;
									let mergeOrderList = []
									orderList.map(element => {
										let exOrder = exOrderStatusMap[element.orderId]
										mergeOrderList.push(
											{
												...element,
												exOrderStatus: exOrder ? exOrder.orderStatus : '',
												storeOrderId: exOrder ? exOrder.id : '',
											})
									})
									console.log(mergeOrderList, '合并转单状态后的订单列表')

									// 初始化选择状态
									mergeOrderList.forEach(item => {
										item.isSelected = false;
									});

									if (_this.page == 1) {
										_this.setData({
											orderingList: mergeOrderList,
											is_result: true,
											selectedOrders: [], // 重置选中状态
											isAllSelected: false
										});
									} else {
										var arr = mergeOrderList
										var arr1 = _this.orderingList;
										var arr2 = arr1.concat(arr);
										_this.setData({
											orderingList: arr2,
											is_result: true
										});
									}
								} else {
									console.log(exRes, '转单状态查询失败')
									util.showText(exRes.data.data || exRes.data.error || exRes.data.message);
								}
							},
							fail: (exRes) => {
								console.log(exRes, '转单状态查询失败')
							}
						})
					} else {
						if (_this.page == 1) {
							_this.setData({
								is_result: false,
								orderingList: '',
								isPageAdd: false
							});
						} else {
							util.showText('已经到底啦~');
							_this.setData({
								is_result: true,
								isPageAdd: false
							});
						}
					}
				}
			);
		},

		// 转单列表
		getTransferOrderList() {
			_this.setData({
				is_result: true
			});

			uni.request({
				url: config.transferfrontList,
				method: 'get',
				header: {
					"Content-Type": "application/json"
				},
				data: {
					index: _this.page,
					pageSize: _this.pageSize,
					type: 2, // type=2 表示"我的转单"
					storeId: app.globalData.storeId,
					startTime: '',
					endTime: '',
				},
				success: (res) => {
					console.log(res, '转单数据列表')
					if (res.data.code == 1) {
						let orderList = res.data.data.list || []

						// 初始化选择状态
						orderList.forEach(item => {
							item.isSelected = false;
						});

						if (_this.page == 1) {
							_this.setData({
								orderingList: orderList,
								is_result: true,
								selectedOrders: [], // 重置选中状态
								isAllSelected: false
							});
						} else {
							var arr = orderList
							var arr1 = _this.orderingList;
							var arr2 = arr1.concat(arr);
							_this.setData({
								orderingList: arr2,
								is_result: true
							});
						}
					} else {
						if (_this.page == 1) {
							_this.setData({
								is_result: false,
								orderingList: '',
								isPageAdd: false
							});
						} else {
							util.showText('已经到底啦~');
							_this.setData({
								is_result: true,
								isPageAdd: false
							});
						}
					}
				},
				fail: (res) => {
					console.log(res, '转单数据获取失败')
					_this.setData({
						is_result: false
					});
					util.showText('获取转单数据失败');
				}
			});
		},

		//撤销转单
		cancelExchangeOrder(e) {
			let that = this
			let orderId = e.currentTarget.dataset.orderId
			// let storeOrderId = e.currentTarget.dataset.storeOrderId

			uni.showModal({
				title: '提示',
				content: '确认撤销转单？',
				success: function (res) {
					if (res.confirm) {
						util.ajax(config.transferCancel, {
							orderId: orderId
						}, function (res) {
							if (res.data.code == 1) {
								uni.showModal({
									title: '提示',
									content: '撤销转单成功',
									showCancel: false,
									success: function (res) {
										that.load()
									}
								});
							} else {
								util.alert(res.data.data);
							}
						})
					}
				}
			})
		},
		backExchangeOrder(e) {
			let that = this
			let orderId = e.currentTarget.dataset.orderId
			// let storeOrderId = e.currentTarget.dataset.storeOrderId

			uni.showModal({
				title: '提示',
				content: '确认回退转单？',
				success: function (res) {
					if (res.confirm) {
						util.ajax(config.transferBack, {
							orderId: orderId
						}, function (res) {
							if (res.data.code == 1) {
								uni.showModal({
									title: '提示',
									content: '回退转单成功',
									showCancel: false,
									success: function (res) {
										that.load()
									}
								});
							} else {
								util.alert(res.data.data);
							}
						})
					}
				}
			})
		},
		// 已完成列表
		getOverOrderList() {
			_this.setData({
				is_result1: true
			});

			util.ajax(
				config.getOverOrderList, {
				storeId: app.globalData.storeId,
				pageSize: _this.pageSize,
				pageNo: _this.page
			},
				function (res) {
					if (res.data.code == 1) {
						if (_this.page == 1) {
							_this.setData({
								orverOrderList: res.data.data.userList,
								is_result1: true,
								orverOrderListCount: res.data.data.count
							});
						} else {
							var arr = res.data.data.userList;
							var arr1 = _this.orverOrderList;
							var arr2 = arr1.concat(arr);
							_this.setData({
								orverOrderList: arr2,
								is_result1: true
							});
						}
					} else {
						if (_this.page == 1) {
							_this.setData({
								is_result1: false,
								orverOrderList: '',
								isPageAdd: false,
								orverOrderListCount: 0
							});
						} else {
							util.showText('已经到底啦~');
							_this.setData({
								is_result1: true,
								isPageAdd: false
							});
						}
					}
				}
			);
		},

		// 手填订单列表
		getHandOverList() {
			_this.setData({
				is_result2: true
			});

			util.ajax(
				config.lookUpHandOrder, {
				storeId: app.globalData.storeId,
				pageNo: _this.page,
				pageSize: 20
			},
				function (res) {
					if (res.data.code == 1) {
						if (!res.data.data.list.length) {
							if (_this.page == 1) {
								_this.setData({
									is_result2: false,
									isPageAdd: false
								});
							} else {
								util.showText('已经到底啦~');
								_this.setData({
									is_result2: true,
									isPageAdd: false
								});
							}
						} else {
							let arr1 = _this.handOrderList;
							let arr2 = res.data.data.list;
							let arr = arr1.concat(arr2);
							_this.setData({
								handOrderList: arr,
								handCount: res.data.data.count,
								is_result2: true
							});
						}
					} else {
						_this.setData({
							is_result2: false,
							handOrderList: '',
							handCount: 0
						});
					}
				}
			);
		},

		addOrder() {
			let that = this;
			util.reto('/pages/orderAdmin/addOrder/addOrder');
		},

		changeHandOrder(e) {
			var state = e.currentTarget.dataset.state,
				order = e.currentTarget.dataset.order;
			util.ajax(
				config.changeHandOrder, {
				orderNum: order,
				orderStatus: state,
				header: 'json'
			},
				function (res) {
					if (res.data.code == 1) {
						util.showSuccess(res.data.data);

						_this.getHandOverList();
					} else {
						util.showText(res.data.data);
					}
				}
			);
		},

		handChoosePeople(e) {
			var datas = e.currentTarget.dataset,
				orderingList = _this.handOrderList;
			var o = {
				orderId: datas.ordernum,
				// 订单编号
				deliveryId: '' // 送水员id
			};

			_this.setData({
				isHandOrder: true
			});

			try {
				wx.setStorageSync('choosePeople', o);
				util.reto('/pages/orderAdmin/choosePeople/choosePeople');
			} catch (e) {
				console.log(e);
			}
		},

		//调用接口 根据输入的地址 获取经纬度
		handGetLocation(address, ordernum) {
			var _this = this; //调用地址解析接口

			wx.showLoading({
				title: '加载中...'
			});
			app.qqmapsdk.geocoder({
				//获取表单传入地址
				address: address,
				//地址参数，例：固定地址，address: '北京市海淀区彩和坊路海淀西大街74号'
				success: function (res) {
					//成功后的回调
					console.log(res, '我是地址解析');
					var res = res.result;
					var latitude = res.location.lat;
					var longitude = res.location.lng;
					var f = latitude + ',' + longitude;
					var to = wx.getStorageSync('storeLocal'); //调用距离计算接口

					app.qqmapsdk.direction({
						mode: 'driving',
						//可选值：'driving'（驾车）、'walking'（步行）、'bicycling'（骑行），不填默认：'driving',可不填
						//from参数不填默认当前地址
						from: f,
						// 起点
						to: to,
						// 终点
						success: function (res) {
							console.log(res);

							_this.handSelectdeliveryrule(ordernum, res.result.routes[0].distance);

							_this.setData({
								distance: Number(res.result.routes[0].distance / 1000).toFixed(1)
							});

							wx.hideLoading();
						},
						fail: function (error) {
							console.error(error);
							wx.hideLoading();
						},
						complete: function (res) {
							console.log(res);
						}
					});
				},
				fail: function (error) {
					console.error(error);
					util.showText('地址信息不正确');
					return;
				}
			});
		},

		// 获取是否设置过规则
		handSelectdeliveryrule(orderNum, discount) {
			util.ajax(
				config.selectdeliveryrule, {
				orderNumber: orderNum,
				discount: discount
			},
				function (res) {
					if (res.data.code == 1) {
						var handIsHaveRule = res.data.data,
							total = 0;

						var total = _this.sumTotal(handIsHaveRule.discountMoneyt, handIsHaveRule.floormoney, handIsHaveRule.shopNumberMoney,
							handIsHaveRule.deliveryMoney);

						_this.setData({
							isHandOrder: false,
							handIsHaveRule: res.data.data,
							total: total,
							p: isHaveRule.deliveryMoney,
							l: isHaveRule.floormoney,
							s: isHaveRule.shopNumberMoney,
							j: isHaveRule.discountMoneyt
						});

						console.log(_this.isHaveSalesman, 'saddsadsa');
						console.log(_this.isHaveRule, '我是大');
					} else {
						_this.setData({
							total: '0.00'
						});

						_this.setData({
							isHandOrder: false,
							isHaveRule: res.data.data,
							p: '0.00',
							l: '0.00',
							s: '0.00',
							j: '0.00'
						});

						console.log(_this.isHaveRule, '我是大12');
					}
				}
			);
		},

		handSubmitInfo() {
			var o = {
				deliverymoney: _this.p || 0,
				// 送水员业务提成
				distance: Number(_this.distance) * 1000,
				// 距离
				distanceMoney: _this.j || 0,
				// 距离费用
				moneytotal: _this.total || 0,
				// 总额
				r1: _this.handAlertGoodsNum,
				// 商品数量
				r2: _this.handAlertOrderNum,
				// 订单编号
				r3: '',
				// 送水员id
				r5: _this.l,
				// 楼层提层
				shopNumberMoney: _this.s,
				// 商品数量费用
				userid: _this.handAlertUserID // 用户ID 有就有没有就传null
			};
			console.log(o);

			_this.setData({
				isHandOrder: true
			});

			wx.setStorageSync('choosePeople', o);
			util.reto('/pages/orderAdmin/choosePeople/choosePeople');
		},

		updateDeliveryInfo(e) {
			let orderNum = e.currentTarget.dataset.ordernum,
				param = {
					orderNumber: orderNum,
					state: 3
				};
			util.ajax(config.updatedeliveryinfo, param, res => {
				if (res.data.code == 1) {
					util.showSuccess('撤销成功');
					this.load();
				} else {
					util.showText(res.data.data);
				}
			})

		},
		// 确认押桶
		confirmPledgeBuck(orderId,userId) {
			// 跳转新页面
			uni.navigateTo({
				url: '/pagesMore/mine/confirmPledgeBuck/confirmPledgeBuck?orderId=' + orderId + "&userId=" + userId
			})
		},

		// 新增确认收款
		confirmCollection(e) {
			let that = this
			let o = {
				orderNum: e.currentTarget.dataset.ordernum,
				state: 1
			}
			util.ajax(config.updatebanktransfer, o, res => {
				if (res.data.code == 1) {
					util.showSuccess('操作成功');
					that.load();
				} else {
					util.showText(res.data.data);
				}
			})
		},
		//转单
		toExchangeOrder(e) {
			let orderId = e.currentTarget.dataset.id
			let userId = e.currentTarget.dataset.userid
			let param = `?orderId=${orderId}&userId=${userId}`
			uni.navigateTo({
				url: '/pagesMore/mine/exchangeOrder/create/createOnline' + param
			})
		},

		// 设置提成 修改提成
		setUpCommission(e) {
			let userid = e.currentTarget.dataset.userid;

			let target = {
				order: 1,
				state: 0,
				index: this.currentTab
			};
			wx.setStorageSync('target', target);

			util.reto(`/pagesMore/userAdmin/addUser/addUser?flag=1&userid=${userid}`);
		},

		setData: function (obj) {
			let that = this;
			let keys = [];
			let val, data;
			Object.keys(obj).forEach(function (key) {
				keys = key.split('.');
				val = obj[key];
				data = that.$data;
				keys.forEach(function (key2, index) {
					if (index + 1 == keys.length) {
						that.$set(data, key2, val);
					} else {
						if (!data[key2]) {
							that.$set(data, key2, {});
						}
					}

					data = data[key2];
				});
			});
			typeof fun == 'function' && fun();
		},

		getafterSaleList() {
			let that = this;
			// if (that.isload == 1) {
			// 	util.showText("操作过于频繁，请稍后再试~");
			// 	return;
			// }
			// that.isload = 1;
			let isurl = config.lookUpSaleList;
			let o = {
				storeId: app.globalData.storeId,
				index: that.page
			};
			util.ajax(isurl, o, function (res) {
				that.isload = 0;
				if (res.data.code == 1) {
					if (that.page != 1 && res.data.data.length == 0) {
						util.showText("已经到到底啦~");
						that.page = that.page - 1;
						return;
					}
					let arr1 = that.afterSaleList;
					let arr2 = res.data.data.list;
					let arr = arr1.concat(arr2);
					that.setData({
						afterSaleList: arr,
					});
				} else {
					util.alert(res.data.data);
				}
			});
		},


		dealSale(e) {
			let that = this;

			if (that.isDeal == 1) {
				util.showText("操作过于频繁，请稍后再试!");
				return false;
			}

			that.isDeal = 1;
			setTimeout(function () {
				that.isDeal = 0;
			}, 2000);
			let state = e.currentTarget.dataset.state;
			let retreatId = e.currentTarget.dataset.id;
			let index = e.currentTarget.dataset.index;
			let isurl = config.dealSaleList;
			let o = {
				retreatId: retreatId,
				state: state
			};
			util.ajax(isurl, o, function (res) {

				if (res.data.code == 1) {
					that.setData({
						page: 1,
						afterSaleList: [],
					});
					util.showText('操作成功');
					that.getafterSaleList();
				} else {
					util.alert(res.data.data);
				}
			});
		},

		goDetail(e) {
			let that = this;
			let id = e.currentTarget.dataset.id;
			util.reto("/pages/mine/after-sales/backDetail/backDetail?id=" + id);
		},

		// 显示收款码弹窗
		showPaymentCode() {
			this.setData({
				showPaymentCodeModal: true
			});
		},
		
		// 关闭收款码弹窗
		closePaymentCode() {
			this.setData({
				showPaymentCodeModal: false
			});
		},

		// 处理搜索输入
		handleSearch() {
			// 输入框内容变化时，设置延迟执行搜索
			clearTimeout(this.searchTimer);
			this.searchTimer = setTimeout(() => {
				this.doSearch();
			}, 500);
		},

		// 执行搜索
		doSearch() {
			this.page = 1;
			this.load();
		},

		// 切换排序方式
		toggleSort() {
			this.sortOrder = this.sortOrder === 'desc' ? 'asc' : 'desc';
			this.page = 1;
			this.load();
		},

		// 显示回退订单弹窗
		openBackOrderModal(e) {
			const orderId = e.currentTarget.dataset.id;
			this.backOrderForm.orderId = orderId;
			this.backOrderForm.reason = '';
			this.showBackOrderModal = true;
		},

		// 关闭回退订单弹窗
		closeBackOrderModal() {
			this.showBackOrderModal = false;
			this.backOrderForm.orderId = '';
			this.backOrderForm.reason = '';
		},

		// 提交回退订单
		submitBackOrder() {
			const that = this;

			if (!this.backOrderForm.reason.trim()) {
				uni.showToast({
					title: '请输入回退原因',
					icon: 'none'
				});
				return;
			}

			uni.showLoading({
				title: '提交中...'
			});

			// 调用回退订单API
			util.ajax(config.szmsendmembercontrollerbackAdmin, {
				orderId: this.backOrderForm.orderId,
				reason: this.backOrderForm.reason
			}, function(res) {
				uni.hideLoading();

				if (res.data.code == 1) {
					uni.showToast({
						title: '回退成功',
						icon: 'success'
					});

					// 关闭弹窗
					that.closeBackOrderModal();

					// 刷新列表
					setTimeout(() => {
						that.load();
					}, 1000);
				} else {
					uni.showToast({
						title: res.data.data || '回退失败',
						icon: 'none'
					});
				}
			});
		},

		// 催单发货
		urgeStore(e) {
			const that = this;
			const orderId = e.currentTarget.dataset.id;

			if (!orderId) {
				uni.showToast({
					title: '订单ID不能为空',
					icon: 'none'
				});
				return;
			}

			uni.showModal({
				title: '提示',
				content: '确认催单发货？',
				success: function (res) {
					if (res.confirm) {
						uni.showLoading({
							title: '催单中...'
						});

						// 调用催单发货API
						util.ajax(config.urgeStore, {
							orderId: orderId
						}, function(res) {
							uni.hideLoading();

							if (res.data.code == 1) {
								uni.showToast({
									title: '催单成功',
									icon: 'success'
								});

								// 刷新订单列表
								setTimeout(() => {
									that.load();
								}, 1000);
							} else {
								uni.showToast({
									title: res.data.data || '催单失败',
									icon: 'none'
								});
							}
						});
					}
				}
			});
		},

		// ========== 批量操作相关方法 ==========

		// 获取当前tab对应的订单列表
		getCurrentOrderList() {
			if (this.currentTab == 3) {
				// 自送订单
				return this.myDispatchList || [];
			} else {
				// 待派单和配送中
				return this.orderingList || [];
			}
		},

		// 切换单个订单选择状态
		toggleOrderSelection(item) {
			let selectedOrders = [...this.selectedOrders];
			let currentList = [...this.getCurrentOrderList()];

			const index = selectedOrders.indexOf(item.orderNum);
			const orderIndex = currentList.findIndex(order => order.orderNum === item.orderNum);

			if (index > -1) {
				// 取消选择
				selectedOrders.splice(index, 1);
				if (orderIndex > -1) {
					currentList[orderIndex].isSelected = false;
				}
			} else {
				// 选择
				selectedOrders.push(item.orderNum);
				if (orderIndex > -1) {
					currentList[orderIndex].isSelected = true;
				}
			}

			// 根据当前tab更新对应的列表
			let updateData = {
				selectedOrders: selectedOrders
			};

			if (this.currentTab == 3) {
				updateData.myDispatchList = currentList;
			} else {
				updateData.orderingList = currentList;
			}

			this.setData(updateData);

			// 更新全选状态
			this.updateSelectAllStatus();
		},

		// 切换全选状态
		toggleSelectAll() {
			let selectedOrders = [];
			let currentList = [...this.getCurrentOrderList()];
			let isAllSelected = false;

			if (this.isAllSelected) {
				// 取消全选
				currentList.forEach(item => {
					item.isSelected = false;
				});
			} else {
				// 全选
				currentList.forEach(item => {
					selectedOrders.push(item.orderNum);
					item.isSelected = true;
				});
				isAllSelected = true;
			}

			// 根据当前tab更新对应的列表
			let updateData = {
				selectedOrders: selectedOrders,
				isAllSelected: isAllSelected
			};

			if (this.currentTab == 3) {
				updateData.myDispatchList = currentList;
			} else {
				updateData.orderingList = currentList;
			}

			this.setData(updateData);
		},

		// 更新全选状态
		updateSelectAllStatus() {
			const totalCount = this.getCurrentOrderList().length;
			const selectedCount = this.selectedOrders.length;
			this.setData({
				isAllSelected: totalCount > 0 && selectedCount === totalCount
			});
		},

		// 清空选择状态
		clearSelection() {
			// 清空选中状态
			let currentList = this.getCurrentOrderList().map(item => {
				return { ...item, isSelected: false };
			});

			let updateData = {
				selectedOrders: [],
				isAllSelected: false
			};

			if (this.currentTab == 3) {
				updateData.myDispatchList = currentList;
			} else {
				updateData.orderingList = currentList;
			}

			this.setData(updateData);
		},

		// 批量派单
		batchDispatch() {
			const that = this;
			if (that.selectedOrders.length <= 0) {
				uni.showToast({
					title: '请选择要派单的订单',
					icon: 'none'
				});
				return;
			}

			// 设置当前订单号为选中的订单号（用逗号分隔）
			that.setData({
				orderNum: that.selectedOrders.join(','),
				isShow_01: true,
				translateY: 0
			});
		},

		// 批量改派
		batchDispatchChange() {
			const that = this;
			if (that.selectedOrders.length <= 0) {
				uni.showToast({
					title: '请选择要改派的订单',
					icon: 'none'
				});
				return;
			}

			// 设置当前订单号为选中的订单号（用逗号分隔）
			that.setData({
				orderNum: that.selectedOrders.join(','),
				isShow_01: true,
				translateY: 0
			});
		},

		// 批量送达
		batchDelivered() {
			const that = this;
			if (that.selectedOrders.length <= 0) {
				uni.showToast({
					title: '请选择要送达的订单',
					icon: 'none'
				});
				return;
			}

			// 确认对话框
			uni.showModal({
				title: '批量送达确认',
				content: `确认批量送达选中的 ${that.selectedOrders.length} 个订单吗？`,
				confirmText: '确定送达',
				cancelText: '取消',
				success: function(res) {
					if (res.confirm) {
						// 执行批量送达
						that.executeBatchDelivered();
					}
				}
			});
		},

		// 执行批量送达
		executeBatchDelivered() {
			const that = this;
			const selectedOrderNums = [...that.selectedOrders]; // 复制数组，避免修改原数组

			// 从订单列表中找到对应的orderId
			let orderIds = [];
			let currentList = that.getCurrentOrderList();
			selectedOrderNums.forEach(orderNum => {
				let order = currentList.find(item => item.orderNum === orderNum);
				if (order && order.orderId) {
					orderIds.push(order.orderId);
				}
			});

			if (orderIds.length === 0) {
				uni.showToast({
					title: '未找到有效的订单ID，请刷新页面后重试',
					icon: 'none'
				});
				return;
			}

			let successCount = 0;
			let failCount = 0;
			let totalCount = orderIds.length;

			// 显示进度提示
			uni.showToast({
				title: `开始批量送达 ${totalCount} 个订单...`,
				icon: 'none'
			});

			// 递归处理每个订单
			const processOrder = (index) => {
				if (index >= orderIds.length) {
					// 所有订单处理完成
					if (failCount === 0) {
						uni.showToast({
							title: `批量送达完成！成功处理 ${successCount} 个订单`,
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: `批量送达完成！成功 ${successCount} 个，失败 ${failCount} 个`,
							icon: 'none'
						});
					}
					// 清空选中的订单
					that.clearSelection();
					// 刷新列表
					that.load();
					return;
				}

				// 处理当前订单
				let orderId = orderIds[index];
				let params = {
					orderId: orderId
				};

				util.ajax(config.eidtDriverState, params, function(res) {
					if (res.data.code == 1) {
						successCount++;
					} else {
						failCount++;
					}

					// 处理下一个订单
					processOrder(index + 1);
				});
			};

			// 开始处理第一个订单
			processOrder(0);
		},
	}
};
</script>
<style>
/* 公共样式 */
.content {
	box-sizing: border-box;
}
.content2 {
	padding: 0 16rpx;
	background: #f8f9fa;
}

.article {
	background-color: white;
	padding: 24rpx;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	border: 1rpx solid #f0f0f0;
	transition: all 0.3s;
}

.article:active {
	transform: scale(0.98);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.font-size-22 {
	font-size: 26rpx;
}

.font-size-24 {
	font-size: 28rpx;
}

.font-size-26 {
	font-size: 30rpx;
}

.font-size-28 {
	font-size: 32rpx;
}

.font-size-30 {
	font-size: 34rpx;
}

.font-size-32 {
	font-size: 36rpx;
}

/* 边距样式 */
.margin-left-10 {
	margin-left: 10rpx;
}

.margin-left-20 {
	margin-left: 20rpx;
}

.margin-left-30 {
	margin-left: 30rpx;
}

.margin-right-10 {
	margin-right: 10rpx;
}

.margin-right-30 {
	margin-right: 30rpx;
}

.margin-bottom-10 {
	margin-bottom: 10rpx;
}

.margin-bottom-20 {
	margin-bottom: 20rpx;
}

.padding {
	padding: 30rpx;
}

.padding-top-20 {
	padding-top: 20rpx;
}

.padding-bottom-20 {
	padding-bottom: 20rpx;
}

/* 布局样式 */
.flex {
	display: flex;
}

.align-items-center {
	align-items: center;
}

.justify-content-between {
	justify-content: space-between;
}

.justify-content-around {
	justify-content: space-around;
}

.text-align-center {
	text-align: center;
}

.position-relative {
	position: relative;
}

.overflow-hidden {
	overflow: hidden;
}

/* 颜色样式 */
.bold {
	font-weight: 600;
}

.color-red {
	color: #ff4d4f;
}

.color-blue {
	color: #1794fd;
}

.color-green {
	color: #07c160;
}

.color-grey {
	color: #888;
}

/* 边框样式 */
.border-top {
	border-top: 1rpx solid #f0f0f0;
}

/* 顶部导航样式 */
.headBox {
	padding: 20rpx 0;
	background-color: white;
	margin-bottom: 16rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.navBarBox {
	display: flex;
	white-space: nowrap;
	height: 80rpx;
}

.barBox {
	display: inline-block;
	padding: 0 30rpx;
	height: 80rpx;
	line-height: 80rpx;
	font-size: 32rpx;
	text-align: center;
	position: relative;
	color: #666;
	transition: all 0.3s;
	font-weight: 500;
}

.select {
	position: relative;
	color: #1794fd;
	font-weight: 600;
}

.a {
	width: 70rpx;
	height: 4rpx;
}

.select .a {
	position: absolute;
	left: 0;
	bottom: 0;
	right: 0;
	top: 64rpx;
	margin: auto;
	width: 70rpx;
	height: 4rpx;
	background-color: #1794fd;
	border-radius: 4rpx;
}

/* swiper样式 */
.mySwiper {
	height: calc(100vh - 120rpx);
}

/* 订单项样式 */
.artTop {
	border-bottom: 1rpx dashed #f0f0f0;
	padding-bottom: 20rpx;
}

.artBottom {
	padding-top: 20rpx;
	padding-bottom: 20rpx;
}

.artTopL {
	width: 70%;
}

.artTopR {
	width: 30%;
}

.artTopL-short {
	width: 70%;
}

.artTopR-short {
	width: 30%;
}

.artTopLTxt {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	color: #666;
}

/* 订单头部样式 */
.order-header {
	margin-bottom: 20rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.order-time {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.order-source-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 12rpx;
}

.time-text {
	font-size: 30rpx;
	color: #666;
}

.mark-text {
	font-size: 28rpx;
	color: #ff6600;
	margin-right: 10rpx;
	background: #fff3e0;
	padding: 2rpx 8rpx;
	border-radius: 4rpx;
}

.order-number {
	font-size: 28rpx;
	color: #999;
}

/* 客户信息样式 */
.customer-info {
	margin-bottom: 20rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx dashed #e0e0e0;
}

.customer-main {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.customer-name-phone {
	display: flex;
	align-items: center;
	flex: 1;
}

.urge-badge {
	background: #ff4757;
	color: white;
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	margin-right: 12rpx;
}

.customer-name {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
	margin-right: 16rpx;
}

.customer-phone {
	font-size: 30rpx;
	color: #666;
	cursor: pointer;
	transition: all 0.2s;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.customer-phone:hover {
	background: #f0f0f0;
	color: #333;
}

.action-buttons {
	display: flex;
	gap: 12rpx;
}

.action-btn {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
	text-align: center;
	min-width: 80rpx;
}

.refresh-btn {
	background: #ffa726;
	color: white;
}

.call-btn {
	background: #42a5f5;
	color: white;
}

.customer-address {
	font-size: 30rpx;
	color: #666;
	line-height: 1.4;
	cursor: pointer;
	transition: all 0.2s;
	padding: 8rpx 0;
	border-radius: 8rpx;
}

.customer-address:hover {
	background: #f7fafc;
	padding: 8rpx 12rpx;
}

/* 复制提示样式 */
.copy-hint {
	font-size: 24rpx;
	color: #3182ce;
	margin-left: 8rpx;
	opacity: 0.8;
	transition: opacity 0.2s;
}

.copy-hint:hover {
	opacity: 1;
}

.address-copy-hint {
	display: block;
	margin-left: 0;
	margin-top: 4rpx;
	font-size: 22rpx;
}

/* 订单金额信息样式 */
.order-amount-info {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
	margin-bottom: 20rpx;
}

.amount-left {
	flex: 1;
}

.total-items {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 8rpx;
}

.amount-details {
	margin-bottom: 8rpx;
}

.amount-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 4rpx;
	max-width: 400rpx;
}

.amount-row.discount .amount-value {
	color: #ff4757;
}

.amount-label {
	font-size: 28rpx;
	color: #666;
}

.amount-value {
	font-size: 28rpx;
	color: #333;
}

.final-amount {
	display: flex;
	align-items: center;
	margin-top: 8rpx;
}

.final-label {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-right: 8rpx;
}

.final-value {
	font-size: 32rpx;
	color: #ff4757;
	font-weight: 600;
}

.more-btn {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #999;
}

.arrow-icon {
	width: 15rpx;
	height: 20rpx;
	margin-left: 5rpx;
}

/* 操作按钮区域样式 */
.action-buttons-area {
	padding-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
	justify-content: flex-end;
}

/* 商品信息简化显示 */
.goods-info-simple {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.goods-item-simple {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12rpx 0;
	border-bottom: 1rpx solid #e9ecef;
}

.goods-item-simple:last-child {
	border-bottom: none;
}

.goods-name {
	flex: 1;
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.goods-quantity {
	font-size: 32rpx;
	color: #ff4757;
	font-weight: 600;
	min-width: 80rpx;
	text-align: right;
}

/* 按钮样式优化 */
.artTopRbtn {
	min-width: 80rpx;
	height: 60rpx;
	text-align: center;
	line-height: 60rpx;
	border: 1rpx solid #09bb07;
	color: #09bb07;
	border-radius: 24rpx;
	display: inline-block;
	margin-left: 8rpx;
	margin-bottom: 8rpx;
	font-size: 28rpx;
	transition: all 0.3s;
	padding: 0rpx 20rpx;
	box-sizing: border-box;
}

.artTopRbtn:active {
	opacity: 0.8;
	transform: scale(0.95);
}

.actArtBtnBlue {
	border: 1rpx solid #1794fd;
	color: #1794fd;
	background: rgba(23, 148, 253, 0.08);
}

.actArtBtnOrange {
	border: 1rpx solid #ffa726;
	color: #ffa726;
	background: rgba(255, 167, 38, 0.08);
}

.actArtBtnPurple {
	border: 1rpx solid #9c27b0;
	color: #9c27b0;
	background: rgba(156, 39, 176, 0.08);
}

.actArtBtnPurple:active {
	background: rgba(156, 39, 176, 0.15);
}

.actArtBtnRed {
	border: 1rpx solid #ff4757;
	color: #ff4757;
	background: rgba(255, 71, 87, 0.08);
}

.actArtBtnGray {
	color: #999;
	background: #f5f5f5;
	border: none;
}

.actArtBtn888 {
	color: #888;
	background: #f5f5f5;
	border: none;
}

/* 更多查看按钮 */
.more {
	display: inline-flex;
	align-items: center;
	color: #999;
	line-height: 50rpx;
}

/* 空数据样式 */
.no {
	text-align: center;
	padding-top: 200rpx;
	padding-bottom: 100rpx;
}

.no image {
	width: 300rpx;
	height: 240rpx;
	margin-bottom: 30rpx;
}

.no .text {
	font-size: 34rpx;
	color: #999;
}

/* 催单图标样式 */
.urge-icon {
	font-size: 26rpx;
	padding: 4rpx 12rpx;
	background: #ff4d4f;
	color: white;
	border-radius: 20rpx;
}

/* 刷新按钮样式 */
.lianying {
	position: fixed;
	right: 0rpx;
	bottom: 100rpx;
	width: 100rpx;
	height: 100rpx;
	background: #1794fd;
	border-radius: 50%;
	color: white;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	z-index: 99;
	box-shadow: 0 4rpx 12rpx rgba(23, 148, 253, 0.3);
}

/* 底部弹出选择层 */
.footerSelectShadow {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 998;
}

.footerSelect {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background: white;
	z-index: 999;
	border-radius: 20rpx 20rpx 0 0;
	transition: transform 0.3s;
}

/* 拍照确认弹窗样式 */
.photo-confirm-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.photo-confirm-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
}

.photo-confirm-content {
	position: relative;
	width: 80%;
	background: #fff;
	border-radius: 16rpx;
	z-index: 1001;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.photo-confirm-header {
	padding: 40rpx;
	text-align: center;
}

.photo-confirm-image {
	width: 200rpx;
	height: 200rpx;
}

.photo-confirm-title {
	font-size: 36rpx;
	color: #333;
	text-align: center;
	padding: 0 40rpx 40rpx;
	font-weight: 500;
}

.photo-confirm-footer {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
}

.photo-confirm-btn {
	flex: 1;
	height: 88rpx;
	line-height: 88rpx;
	text-align: center;
	font-size: 36rpx;
	border-radius: 0;
	border: none;
}

.photo-confirm-btn::after {
	border: none;
}

.photo-confirm-btn.cancel {
	background: #f7f7f7;
	color: #666;
	border-right: 1rpx solid #f0f0f0;
}

.photo-confirm-btn.confirm {
	background: #07c160;
	color: #fff;
}

/* 当只有确认按钮时，让按钮占满整个宽度并居中 */
.photo-confirm-footer.single-button .photo-confirm-btn.confirm {
	border-radius: 0 0 12rpx 12rpx;
	background: #1890ff;
	font-weight: 600;
}

/* 关闭按钮样式 */
.photo-confirm-close {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.1);
	border-radius: 50%;
	z-index: 1;
}

.close-icon {
	font-size: 40rpx;
	color: #666;
	line-height: 1;
}

/* 撤销押桶按钮 */
.go-back-list {
	color: #1794fd;
	font-size: 24rpx;
	background: rgba(23, 148, 253, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 24rpx;
}
.go-back-list-confirm {
	color: #07c160;
	font-size: 24rpx;
	background: rgba(7, 193, 96, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 24rpx;
}

/* 送水员选择样式 */
.driver-list {
	width: 100%;
	padding: 0 30rpx;
}

.driver-item {
	padding: 30rpx 0;
	border-bottom: 1px solid #eee;
	font-size: 34rpx;
}

.driver-item-selected {
	color: #07c160;
	font-weight: bold;
}

/* 门店收款码悬浮窗样式 */
.payment-code-float {
	position: fixed;
	left: 30rpx;
	bottom: 100rpx;
	width: 100rpx;
	height: 100rpx;
	background: #07c160;
	border-radius: 50%;
	color: white;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	z-index: 99;
	box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.payment-code-float image {
	width: 50rpx;
	height: 50rpx;
	margin-bottom: 4rpx;
}

/* 收款码弹窗样式 */
.payment-code-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.payment-code-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1001;
}

.payment-code-content {
	position: relative;
	width: 600rpx;
	background: #fff;
	border-radius: 16rpx;
	z-index: 1002;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.payment-code-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.payment-code-title {
	font-size: 36rpx;
	font-weight: 500;
}

.payment-code-close {
	font-size: 50rpx;
	color: #999;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.payment-code-body {
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.payment-code-img {
	width: 400rpx;
	height: 400rpx;
	margin-bottom: 20rpx;
}

.payment-code-tips {
	font-size: 32rpx;
	color: #666;
}

/* 搜索框和排序按钮样式优化 */
.search-sort-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx;
	background-color: white;
	margin-bottom: 16rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #f8f9fa;
	border-radius: 24rpx;
	padding: 12rpx 20rpx;
	flex: 1;
	margin-right: 16rpx;
}

.search-box input {
	flex: 1;
	height: 44rpx;
	font-size: 30rpx;
	border: none;
	background: transparent;
}

.search-btn {
	background-color: #1890ff;
	color: white;
	padding: 12rpx 24rpx;
	border-radius: 24rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 80rpx;
	height: 44rpx;
}

.sort-box-asc {
	display: flex;
	align-items: center;
	background-color: #42a5f5;
	border-radius: 24rpx;
	padding: 12rpx 20rpx;
	font-size: 28rpx;
	color: #ffffff;
	box-shadow: 0 2rpx 6rpx rgba(66, 165, 245, 0.3);
}

.sort-box-desc {
	display: flex;
	align-items: center;
	background-color: #66bb6a;
	border-radius: 24rpx;
	padding: 12rpx 20rpx;
	font-size: 28rpx;
	color: #ffffff;
	box-shadow: 0 2rpx 6rpx rgba(102, 187, 106, 0.3);
}

/* 配送中订单特有样式 */
.delivery-status-badges {
	display: flex;
	gap: 8rpx;
	margin-top: 8rpx;
}

.badge-pledge {
	background: #ff4757;
	color: white;
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-weight: 600;
}

.badge-debt {
	background: #3742fa;
	color: white;
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-weight: 600;
}

.delivery-status {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 20rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: 600;
	text-align: center;
}

.status-delivering {
	background: #ffa726;
	color: white;
}

.status-delivered {
	background: #66bb6a;
	color: white;
}

.status-signed {
	background: #42a5f5;
	color: white;
}

.status-completed {
	background: #9e9e9e;
	color: white;
}

.ya-tong-notice {
	background: #e3f2fd;
	border-left: 4rpx solid #2196f3;
	padding: 12rpx 16rpx;
	margin-bottom: 20rpx;
	border-radius: 0 8rpx 8rpx 0;
}

.ya-tong-text {
	color: #1976d2;
	font-size: 28rpx;
	font-weight: 600;
}

/* 待退货页面样式 */
.return-header {
	margin-bottom: 20rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.return-order-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.return-order-number {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.return-type-badge {
	background: #fff3cd;
	border: 1rpx solid #ffeaa7;
	border-radius: 12rpx;
	padding: 6rpx 12rpx;
}

.return-type-text {
	font-size: 26rpx;
	color: #856404;
	font-weight: 600;
}

.return-customer-info {
	margin-bottom: 20rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx dashed #e0e0e0;
}

.customer-detail {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 16rpx;
}

.customer-row {
	display: flex;
	margin-bottom: 8rpx;
}

.customer-row:last-child {
	margin-bottom: 0;
}

.customer-label {
	font-size: 28rpx;
	color: #666;
	min-width: 120rpx;
}

.customer-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.return-goods-info {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.return-goods-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12rpx 0;
	border-bottom: 1rpx solid #e9ecef;
}

.return-goods-item:last-child {
	border-bottom: none;
}

.return-goods-name {
	flex: 1;
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.return-goods-details {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.return-goods-price {
	font-size: 28rpx;
	color: #666;
}

.return-goods-quantity {
	font-size: 32rpx;
	color: #ff4757;
	font-weight: 600;
	min-width: 80rpx;
	text-align: right;
}

.return-action-area {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
}

.return-amount-info {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.return-amount-label {
	font-size: 28rpx;
	color: #666;
}

.return-amount-value {
	font-size: 32rpx;
	color: #ff4757;
	font-weight: 600;
}

.return-action-buttons {
	display: flex;
	gap: 12rpx;
}

.return-buttons-pending {
	display: flex;
	gap: 12rpx;
}

.return-btn {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: 500;
	text-align: center;
	min-width: 80rpx;
	transition: all 0.3s;
}

.return-btn:active {
	opacity: 0.8;
	transform: scale(0.95);
}

.return-btn-reject {
	background: #fff5f5;
	color: #e53e3e;
	border: 1rpx solid #feb2b2;
}

.return-btn-approve {
	background: #f0fff4;
	color: #38a169;
	border: 1rpx solid #9ae6b4;
}

.return-status-approved {
	color: #38a169;
	font-size: 28rpx;
	font-weight: 600;
	padding: 8rpx 16rpx;
	background: #f0fff4;
	border-radius: 20rpx;
}

.return-status-rejected {
	color: #e53e3e;
	font-size: 28rpx;
	font-weight: 600;
	padding: 8rpx 16rpx;
	background: #fff5f5;
	border-radius: 20rpx;
}

/* 已完成订单页面样式 */
.completed-summary {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
}

.summary-item {
	display: flex;
	align-items: center;
	justify-content: center;
}

.summary-label {
	font-size: 32rpx;
	color: white;
	font-weight: 500;
}

.summary-value {
	font-size: 36rpx;
	color: white;
	font-weight: 700;
	margin-left: 8rpx;
}

.completed-actions {
	display: flex;
	gap: 8rpx;
	margin-top: 8rpx;
}

.action-btn-small {
	padding: 4rpx 12rpx;
	border-radius: 16rpx;
	font-size: 24rpx;
	font-weight: 500;
	text-align: center;
	transition: all 0.3s;
}

.action-btn-small:active {
	opacity: 0.8;
	transform: scale(0.95);
}

.cancel-btn {
	background: rgba(255, 167, 38, 0.1);
	color: #ffa726;
	border: 1rpx solid #ffa726;
}

.confirm-btn {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
	border: 1rpx solid #4caf50;
}

.bucket-status-badge {
	background: #e8f5e8;
	color: #4caf50;
	font-size: 24rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 600;
	border: 1rpx solid #c8e6c9;
}

.delivery-info {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	border-left: 4rpx solid #42a5f5;
}

.delivery-person {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.delivery-detail {
	display: flex;
	align-items: center;
}

.delivery-label {
	font-size: 28rpx;
	color: #666;
	min-width: 120rpx;
}

.delivery-value {
	font-size: 28rpx;
	color: #1976d2;
	font-weight: 600;
}

/* 今日订单页面样式 */
.today-summary {
	display: flex;
	gap: 16rpx;
	margin-bottom: 20rpx;
}

.summary-card {
	flex: 1;
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	border-radius: 16rpx;
	padding: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.15);
}

.summary-card:last-child {
	background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
	box-shadow: 0 4rpx 12rpx rgba(72, 52, 212, 0.15);
}

.summary-card .summary-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.summary-card .summary-label {
	font-size: 28rpx;
	color: white;
	font-weight: 500;
}

.summary-card .summary-value {
	font-size: 32rpx;
	color: white;
	font-weight: 700;
}

.urge-badge {
	background: #ff4757;
	color: white;
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-weight: 600;
	margin-right: 8rpx;
}

.order-status-actions {
	margin-bottom: 20rpx;
}

.status-buttons {
	display: flex;
	justify-content: flex-end;
	gap: 8rpx;
	flex-wrap: wrap;
}

.status-btn {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: 500;
	text-align: center;
	transition: all 0.3s;
	border: 1rpx solid transparent;
}

.status-btn:active {
	opacity: 0.8;
	transform: scale(0.95);
}

.status-btn-primary {
	background: #1976d2;
	color: white;
	border-color: #1976d2;
}

.status-btn-warning {
	background: #ffa726;
	color: white;
	border-color: #ffa726;
}

.status-badge {
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 26rpx;
	font-weight: 500;
	text-align: center;
}

.status-shipped {
	background: #e3f2fd;
	color: #1976d2;
	border: 1rpx solid #bbdefb;
}

.status-signed {
	background: #e8f5e8;
	color: #4caf50;
	border: 1rpx solid #c8e6c9;
}

.status-completed {
	background: #f3e5f5;
	color: #9c27b0;
	border: 1rpx solid #e1bee7;
}

.status-reviewed {
	background: #fff3e0;
	color: #ff9800;
	border: 1rpx solid #ffcc02;
}

.status-cancelled {
	background: #ffebee;
	color: #f44336;
	border: 1rpx solid #ffcdd2;
}

.status-rejected {
	background: #fce4ec;
	color: #e91e63;
	border: 1rpx solid #f8bbd9;
}

.status-return-pending {
	background: #fff8e1;
	color: #ff8f00;
	border: 1rpx solid #ffecb3;
}

.status-return-approved {
	background: #e8f5e8;
	color: #4caf50;
	border: 1rpx solid #c8e6c9;
}

.status-return-rejected {
	background: #ffebee;
	color: #f44336;
	border: 1rpx solid #ffcdd2;
}

.delivery-actions {
	display: flex;
	gap: 8rpx;
	margin-top: 12rpx;
}

/* 全部订单页面样式 */
.all-orders-summary {
	background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(44, 62, 80, 0.15);
}

.all-orders-summary .summary-item {
	display: flex;
	align-items: center;
	justify-content: center;
}

.all-orders-summary .summary-label {
	font-size: 28rpx;
	color: white;
	font-weight: 500;
}

.all-orders-summary .summary-value {
	font-size: 32rpx;
	color: white;
	font-weight: 700;
	margin-left: 8rpx;
}

.all-orders-actions {
	display: flex;
	gap: 8rpx;
	margin-top: 8rpx;
}

/* 回退订单弹窗样式 */
.back-order-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-order-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
}

.back-order-content {
	position: relative;
	width: 80%;
	max-width: 500rpx;
	background: white;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.back-order-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.back-order-title {
	font-size: 40rpx;
	font-weight: 600;
	color: #333;
}

.back-order-close {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 44rpx;
	color: #999;
	cursor: pointer;
}

.back-order-body {
	padding: 32rpx;
}

.back-order-form .form-item {
	margin-bottom: 24rpx;
}

.back-order-form .form-label {
	display: block;
	font-size: 32rpx;
	color: #333;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.back-order-form .form-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 32rpx;
	color: #333;
	background: #fafafa;
	box-sizing: border-box;
	resize: none;
}

.back-order-form .form-textarea:focus {
	border-color: #1890ff;
	background: white;
}

.back-order-footer {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
}

.back-order-btn {
	flex: 1;
	height: 88rpx;
	border: none;
	font-size: 36rpx;
	font-weight: 500;
	cursor: pointer;
}

.back-order-btn.cancel {
	background: #f5f5f5;
	color: #666;
}

.back-order-btn.confirm {
	background: #1890ff;
	color: white;
}

.back-order-btn.confirm:active {
	background: #096dd9;
}

/* 批量操作样式 */
.batch-operation-container {
	background-color: #f8f8f8;
	border-bottom: 1rpx solid #e0e0e0;
	padding: 20rpx;
}

.batch-select-area {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.select-all-checkbox {
	display: flex;
	align-items: center;
	cursor: pointer;
}

.checkbox-icon {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #ddd;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15rpx;
	background-color: white;
}

.checkbox-icon.checked {
	background-color: #007aff;
	border-color: #007aff;
}

.check-mark {
	color: white;
	font-size: 24rpx;
	font-weight: bold;
}

.select-all-text {
	font-size: 28rpx;
	color: #333;
}

.batch-buttons-area {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
}

.batch-btn {
	padding: 15rpx 30rpx;
	border-radius: 8rpx;
	font-size: 26rpx;
	color: white;
	text-align: center;
	cursor: pointer;
	min-width: 160rpx;
}

.batch-btn-primary {
	background-color: #007aff;
}

.batch-btn-success {
	background-color: #34c759;
}

.batch-btn:active {
	opacity: 0.8;
}

/* 订单项内联复选框样式 */
.order-checkbox-inline {
	margin-right: 12rpx;
}

.order-checkbox {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #ddd;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: white;
}

.order-checkbox.checked {
	background-color: #007aff;
	border-color: #007aff;
}

.order-checkbox .check-mark {
	color: white;
	font-size: 18rpx;
	font-weight: bold;
}

/* 操作记录按钮样式 */
.actArtBtnGreen {
	background: #67c23a;
	color: white;
	border: 1px solid #67c23a;
}

.actArtBtnGreen:active {
	background: #5daf34;
	border-color: #5daf34;
}

/* 订单操作记录弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.order-log-modal {
	width: 90%;
	max-width: 650rpx;
	max-height: 80vh;
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
	background: #fafafa;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin: 0;
}

.modal-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	color: #999;
	cursor: pointer;
	border-radius: 50%;
	transition: all 0.3s;
}

.modal-close:hover {
	background: #f5f5f5;
	color: #666;
}

.order-log-body {
	max-height: 60vh;
	overflow-y: auto;
	padding: 0;
}

.modal-footer {
	padding: 20rpx 30rpx;
	border-top: 1px solid #f0f0f0;
	background: #fafafa;
	display: flex;
	justify-content: center;
}

.modal-btn {
	padding: 20rpx 40rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
	cursor: pointer;
	transition: all 0.3s;
}

.modal-btn-confirm {
	background: #007aff;
	color: white;
}

.modal-btn-confirm:hover {
	background: #0056cc;
}

.loading-container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 80rpx 40rpx;
	min-height: 200rpx;
}

.loading-text {
	color: #999;
	font-size: 28rpx;
	margin-top: 20rpx;
}

.loading-container::before {
	content: '';
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.order-log-content {
	padding: 30rpx;
	position: relative;
}

.order-log-content:before {
	content: '';
	position: absolute;
	left: 22rpx;
	top: 60rpx;
	bottom: 60rpx;
	width: 2rpx;
	background: linear-gradient(to bottom, #007aff, #e0e0e0);
	z-index: 0;
}

.log-item {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border: 1px solid #f0f0f0;
	position: relative;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s;
	z-index: 1;
	margin-left: 30rpx;
}

.log-item:hover {
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	transform: translateY(-2rpx);
}

.log-item:last-child {
	margin-bottom: 0;
}

.log-item:before {
	content: '';
	position: absolute;
	left: -1rpx;
	top: 0;
	bottom: 0;
	width: 6rpx;
	background: linear-gradient(135deg, #007aff, #5ac8fa);
	border-radius: 3rpx 0 0 3rpx;
}

.log-item:after {
	content: '';
	position: absolute;
	left: -38rpx;
	top: 35rpx;
	width: 16rpx;
	height: 16rpx;
	background: #007aff;
	border-radius: 50%;
	border: 3rpx solid #fff;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	z-index: 2;
}

.log-time {
	font-size: 26rpx;
	color: #007aff;
	margin-bottom: 15rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
}

.log-time:before {
	content: '🕐';
	margin-right: 10rpx;
	font-size: 24rpx;
}

.log-content {
	font-size: 30rpx;
	color: #333;
	line-height: 1.6;
	padding-left: 10rpx;
	margin-bottom: 10rpx;
}

.log-detail {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	padding-left: 10rpx;
	background: #f0f0f0;
	padding: 15rpx;
	border-radius: 6rpx;
	margin-top: 10rpx;
}

.no-log {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 80rpx 40rpx;
	min-height: 200rpx;
}

.no-log:before {
	content: '📝';
	font-size: 80rpx;
	margin-bottom: 20rpx;
	opacity: 0.3;
}

.no-log-text {
	color: #999;
	font-size: 28rpx;
	text-align: center;
}

/* 押桶申请页面的操作记录按钮样式 */
.go-back-list-log {
	background: #67c23a;
	color: white;
	border: 1px solid #67c23a;
	padding: 10rpx 20rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	margin-left: 10rpx;
	transition: all 0.3s;
}

.go-back-list-log:active {
	background: #5daf34;
	border-color: #5daf34;
	transform: scale(0.95);
}

/* 移动端优化 */
@media (max-width: 750rpx) {
	.order-log-modal {
		width: 95%;
		max-height: 85vh;
	}

	.modal-header {
		padding: 25rpx;
	}

	.modal-title {
		font-size: 30rpx;
	}

	.order-log-content {
		padding: 20rpx;
	}

	.log-item {
		padding: 25rpx;
		margin-left: 25rpx;
	}

	.log-content {
		font-size: 28rpx;
	}
}

/* 滚动条样式优化 */
.order-log-body::-webkit-scrollbar {
	width: 6rpx;
}

.order-log-body::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 3rpx;
}

.order-log-body::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 3rpx;
}

.order-log-body::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}

/* 转单订单样式 */
.transfer-order-item {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid #f0f0f0;
}

.transfer-header-info {
	margin-bottom: 20rpx;
}

.transfer-header-info .shop-name,
.transfer-header-info .order-time {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.transfer-header-info .label {
	color: #666;
	font-size: 28rpx;
	margin-right: 10rpx;
	min-width: 140rpx;
}

.transfer-header-info .value {
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
}

.transfer-user-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.transfer-user-info .user-details {
	flex: 1;
}

.transfer-user-info .user-name {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.transfer-user-info .user-name text:first-child {
	color: #333;
	font-size: 30rpx;
	font-weight: 500;
	margin-right: 20rpx;
}

.transfer-user-info .user-phone {
	color: #666;
	font-size: 26rpx;
}

.transfer-user-info .user-address {
	color: #666;
	font-size: 26rpx;
	line-height: 1.4;
}

.transfer-user-info .call-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #007aff;
	border-radius: 50%;
}

.transfer-user-info .call-btn image {
	width: 32rpx;
	height: 32rpx;
}

.transfer-goods {
	margin-bottom: 20rpx;
}

.transfer-goods .goods-item {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
	padding: 15rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.transfer-goods .goods-image {
	width: 80rpx;
	height: 80rpx;
	margin-right: 20rpx;
	border-radius: 8rpx;
	overflow: hidden;
}

.transfer-goods .imgItem {
	width: 100%;
	height: 100%;
}

.transfer-goods .goods-info {
	flex: 1;
}

.transfer-goods .goods-title {
	color: #333;
	font-size: 28rpx;
	margin-bottom: 8rpx;
	line-height: 1.3;
}

.transfer-goods .goods-count {
	color: #666;
	font-size: 24rpx;
}

.transfer-order-summary {
	margin-bottom: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.transfer-order-summary .order-total {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.transfer-order-summary .order-total:last-child {
	margin-bottom: 0;
}

.transfer-order-summary .order-price {
	color: #ff6b35;
	font-size: 28rpx;
	font-weight: 600;
}

.transfer-order-summary .distance-info {
	color: #666;
	font-size: 24rpx;
	margin-top: 10rpx;
}

.transfer-action-buttons {
	display: flex;
	gap: 20rpx;
	justify-content: flex-end;
}

.transfer-btn {
	padding: 16rpx 32rpx;
	border-radius: 8rpx;
	font-size: 26rpx;
	text-align: center;
	min-width: 120rpx;
}

.transfer-btn-detail {
	background: #007aff;
	color: #fff;
}

.transfer-btn-cancel {
	background: #ff3b30;
	color: #fff;
}
</style>
